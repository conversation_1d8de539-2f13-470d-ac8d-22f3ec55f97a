import { Hono } from 'hono';
import type { D1Database, KVNamespace, R2Bucket, Fetcher, SendEmail } from '@cloudflare/workers-types';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { serveStatic } from 'hono/cloudflare-workers';
import { authRoutes } from './routes/auth';
import { invoices } from './routes/invoices';
import { clientRoutes } from './routes/clients';
import { anaf } from './routes/anaf';
import { productRoutes } from './routes/products';
import { companyRoutes } from './routes/companies';
import { dashboardRoutes } from './routes/dashboard';
import { reportRoutes } from './routes/reports';
import { settingsRoutes } from './routes/settings';
import { paymentRoutes } from './routes/payments';
import { creditNoteRoutes } from './routes/credit_notes';
import { receiptRoutes } from './routes/receipts';
import { authMiddleware } from './middleware/auth';
import { errorHandler } from './middleware/error-handler';

export interface Env {
  DB: D1Database;
  KV: KVNamespace;
  BUCKET: R2Bucket;
  EMAIL: SendEmail;
  JWT_SECRET: string;
  ENVIRONMENT: string;
  COMPANY_NAME: string;
  DEFAULT_TIMEZONE: string;
  ENCRYPTION_KEY: string;
  BROWSER: Fetcher;
  INVOICING_KV: KVNamespace;
  INVOICING_FILES: R2Bucket;
}

const app = new Hono<{ Bindings: Env }>();

// Global middleware
app.use('*', logger());
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// Serve static assets
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
app.use('/assets/*', serveStatic({ root: './' }));

// Serve the main application HTML
app.get('/', (c) => {
  return c.html(`<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistem Facturare Românesc</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar-transition { transition: all 0.3s ease; }
        .fade-in { animation: fadeIn 0.5s ease-in; }
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        .loading { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
        .modal { backdrop-filter: blur(4px); }
        .btn-primary { @apply bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors; }
        .btn-secondary { @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors; }
        .btn-danger { @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors; }
        .input-field { @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500; }
        .card { @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6; }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div class="text-center">
            <i class="fas fa-file-invoice text-6xl text-indigo-600 mb-4 loading"></i>
            <h2 class="text-xl font-semibold text-gray-900">Se încarcă...</h2>
        </div>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="hidden min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <i class="fas fa-file-invoice text-6xl text-indigo-600 mb-4"></i>
                <h2 class="text-3xl font-bold text-gray-900">Sistem Facturare Românesc</h2>
                <p class="mt-2 text-sm text-gray-600">Autentifică-te pentru a accesa contul tău</p>
            </div>
            <form id="login-form" class="mt-8 space-y-6">
                <div class="space-y-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input id="email" name="email" type="email" required class="input-field" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">Parolă</label>
                        <input id="password" name="password" type="password" required class="input-field" placeholder="Parola ta">
                    </div>
                </div>
                <div>
                    <button type="submit" class="w-full btn-primary">
                        <span id="login-text">Autentificare</span>
                        <i id="login-spinner" class="fas fa-spinner fa-spin hidden ml-2"></i>
                    </button>
                </div>
                <div class="text-center">
                    <a href="#" id="show-register" class="text-indigo-600 hover:text-indigo-500 text-sm">Nu ai cont? Înregistrează-te</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Register Screen -->
    <div id="register-screen" class="hidden min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <i class="fas fa-file-invoice text-6xl text-indigo-600 mb-4"></i>
                <h2 class="text-3xl font-bold text-gray-900">Înregistrare</h2>
                <p class="mt-2 text-sm text-gray-600">Creează un cont nou</p>
            </div>
            <form id="register-form" class="mt-8 space-y-6">
                <div class="space-y-4">
                    <div>
                        <label for="reg-name" class="block text-sm font-medium text-gray-700">Nume complet</label>
                        <input id="reg-name" name="name" type="text" required class="input-field" placeholder="Numele tău">
                    </div>
                    <div>
                        <label for="reg-email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input id="reg-email" name="email" type="email" required class="input-field" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label for="reg-password" class="block text-sm font-medium text-gray-700">Parolă</label>
                        <input id="reg-password" name="password" type="password" required class="input-field" placeholder="Parola ta">
                    </div>
                    <div>
                        <label for="company-name" class="block text-sm font-medium text-gray-700">Numele companiei</label>
                        <input id="company-name" name="company_name" type="text" required class="input-field" placeholder="SC Exemplu SRL">
                    </div>
                    <div>
                        <label for="company-cui" class="block text-sm font-medium text-gray-700">CUI</label>
                        <input id="company-cui" name="cui" type="text" required class="input-field" placeholder="RO12345678">
                    </div>
                </div>
                <div>
                    <button type="submit" class="w-full btn-primary">
                        <span id="register-text">Înregistrare</span>
                        <i id="register-spinner" class="fas fa-spinner fa-spin hidden ml-2"></i>
                    </button>
                </div>
                <div class="text-center">
                    <a href="#" id="show-login" class="text-indigo-600 hover:text-indigo-500 text-sm">Ai deja cont? Autentifică-te</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="hidden min-h-screen bg-gray-50">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform -translate-x-full lg:translate-x-0 sidebar-transition">
            <div class="flex items-center justify-center h-16 bg-indigo-600">
                <i class="fas fa-file-invoice text-2xl text-white mr-2"></i>
                <span class="text-white text-lg font-semibold">Facturare RO</span>
            </div>
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <a href="#" data-page="dashboard" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 active">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>
                    <a href="#" data-page="invoices" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-file-invoice mr-3"></i>
                        Facturi
                    </a>
                    <a href="#" data-page="clients" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-users mr-3"></i>
                        Clienți
                    </a>
                    <a href="#" data-page="products" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-box mr-3"></i>
                        Produse
                    </a>
                    <a href="#" data-page="payments" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-credit-card mr-3"></i>
                        Plăți
                    </a>
                    <a href="#" data-page="reports" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-chart-bar mr-3"></i>
                        Rapoarte
                    </a>
                    <a href="#" data-page="recurring" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-sync-alt mr-3"></i>
                        Facturi recurente
                    </a>
                    <a href="#" data-page="settings" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                        <i class="fas fa-cog mr-3"></i>
                        Setări
                    </a>
                </div>
            </nav>
            <div class="absolute bottom-0 w-full p-4">
                <button id="logout-btn" class="w-full flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100">
                    <i class="fas fa-sign-out-alt mr-3"></i>
                    Deconectare
                </button>
            </div>
        </div>

        <!-- Mobile menu button -->
        <div class="lg:hidden">
            <button id="mobile-menu-btn" class="fixed top-4 left-4 z-50 p-2 rounded-md bg-white shadow-lg">
                <i class="fas fa-bars text-gray-600"></i>
            </button>
        </div>

        <!-- Main content -->
        <div class="lg:ml-64">
            <!-- Top bar -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-4">
                        <h1 id="page-title" class="text-2xl font-semibold text-gray-900">Dashboard</h1>
                        <div class="flex items-center space-x-4">
                            <span id="user-name" class="text-sm text-gray-700"></span>
                            <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page content -->
            <main class="p-4 sm:p-6 lg:p-8">
                <div id="page-content" class="fade-in">
                    <!-- Content will be loaded here -->
                </div>
            </main>
        </div>
    </div>

    <!-- Notification Toast -->
    <div id="toast" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex items-center">
                <div id="toast-icon" class="flex-shrink-0 mr-3">
                    <i class="fas fa-check-circle text-green-500"></i>
                </div>
                <div>
                    <p id="toast-message" class="text-sm font-medium text-gray-900"></p>
                </div>
                <button id="toast-close" class="ml-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Global app state
        const app = {
            user: null,
            token: null,
            currentPage: 'dashboard'
        };

        // API helper
        const api = {
            baseUrl: '/api',

            async request(endpoint, options = {}) {
                const url = \`\${this.baseUrl}\${endpoint}\`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(app.token && { 'Authorization': \`Bearer \${app.token}\` })
                    },
                    ...options
                };

                if (config.body && typeof config.body === 'object') {
                    config.body = JSON.stringify(config.body);
                }

                try {
                    const response = await fetch(url, config);
                    const data = await response.json();

                    if (!response.ok) {
                        throw new Error(data.message || 'Request failed');
                    }

                    return data;
                } catch (error) {
                    console.error('API Error:', error);
                    throw error;
                }
            },

            get(endpoint) { return this.request(endpoint); },
            post(endpoint, body) { return this.request(endpoint, { method: 'POST', body }); },
            put(endpoint, body) { return this.request(endpoint, { method: 'PUT', body }); },
            delete(endpoint) { return this.request(endpoint, { method: 'DELETE' }); }
        };

        // Utility functions
        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            const icon = document.getElementById('toast-icon');
            const messageEl = document.getElementById('toast-message');

            const icons = {
                success: 'fas fa-check-circle text-green-500',
                error: 'fas fa-exclamation-circle text-red-500',
                warning: 'fas fa-exclamation-triangle text-yellow-500',
                info: 'fas fa-info-circle text-blue-500'
            };

            icon.innerHTML = \`<i class="\${icons[type]}"></i>\`;
            messageEl.textContent = message;

            toast.classList.remove('hidden');
            setTimeout(() => toast.classList.add('hidden'), 5000);
        }

        function formatCurrency(amount, currency = 'RON') {
            return new Intl.NumberFormat('ro-RO', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('ro-RO');
        }

        // Authentication functions
        async function login(email, password) {
            try {
                const response = await api.post('/auth/login', { email, password });
                if (response.success) {
                    app.token = response.token;
                    app.user = response.user;
                    localStorage.setItem('token', app.token);
                    localStorage.setItem('user', JSON.stringify(app.user));
                    showApp();
                    return true;
                }
            } catch (error) {
                showToast(error.message, 'error');
                return false;
            }
        }

        async function register(formData) {
            try {
                const response = await api.post('/auth/register', formData);
                if (response.success) {
                    showToast('Contul a fost creat cu succes! Te poți autentifica acum.', 'success');
                    showLogin();
                    return true;
                }
            } catch (error) {
                showToast(error.message, 'error');
                return false;
            }
        }

        function logout() {
            app.token = null;
            app.user = null;
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            showLogin();
        }

        // UI functions
        function showLogin() {
            document.getElementById('loading-screen').classList.add('hidden');
            document.getElementById('register-screen').classList.add('hidden');
            document.getElementById('app').classList.add('hidden');
            document.getElementById('login-screen').classList.remove('hidden');
        }

        function showRegister() {
            document.getElementById('loading-screen').classList.add('hidden');
            document.getElementById('login-screen').classList.add('hidden');
            document.getElementById('app').classList.add('hidden');
            document.getElementById('register-screen').classList.remove('hidden');
        }

        function showApp() {
            document.getElementById('loading-screen').classList.add('hidden');
            document.getElementById('login-screen').classList.add('hidden');
            document.getElementById('register-screen').classList.add('hidden');
            document.getElementById('app').classList.remove('hidden');

            if (app.user) {
                document.getElementById('user-name').textContent = app.user.name;
            }

            loadPage('dashboard');
        }

        // Page loading functions
        async function loadPage(page) {
            app.currentPage = page;

            // Update navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active', 'bg-indigo-100', 'text-indigo-700');
                if (link.dataset.page === page) {
                    link.classList.add('active', 'bg-indigo-100', 'text-indigo-700');
                }
            });

            // Update page title
            const titles = {
                dashboard: 'Dashboard',
                invoices: 'Facturi',
                clients: 'Clienți',
                products: 'Produse',
                payments: 'Plăți',
                reports: 'Rapoarte',
                recurring: 'Facturi recurente',
                settings: 'Setări'
            };
            document.getElementById('page-title').textContent = titles[page] || 'Dashboard';

            // Load page content
            const content = document.getElementById('page-content');
            content.innerHTML = '<div class="flex justify-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i></div>';

            try {
                switch (page) {
                    case 'dashboard':
                        await loadDashboard();
                        break;
                    case 'invoices':
                        await loadInvoices();
                        break;
                    case 'clients':
                        await loadClients();
                        break;
                    case 'products':
                        await loadProducts();
                        break;
                    case 'payments':
                        await loadPayments();
                        break;
                    case 'reports':
                        await loadReports();
                        break;
                    case 'recurring':
                        await loadRecurring();
                        break;
                    case 'settings':
                        await loadSettings();
                        break;
                    default:
                        await loadDashboard();
                }
            } catch (error) {
                content.innerHTML = \`
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Eroare la încărcarea paginii</h3>
                        <p class="text-gray-600">\${error.message}</p>
                        <button onclick="loadPage('\${page}')" class="mt-4 btn-primary">Încearcă din nou</button>
                    </div>
                \`;
            }
        }

        // Dashboard implementation
        async function loadDashboard() {
            try {
                const [stats, insights] = await Promise.all([
                    api.get('/dashboard/stats'),
                    api.get('/dashboard/insights')
                ]);

                const content = document.getElementById('page-content');
                content.innerHTML = \`
                    <div class="space-y-6">
                        <!-- Stats Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="card">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-blue-100">
                                        <i class="fas fa-file-invoice text-blue-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Facturi luna aceasta</p>
                                        <p class="text-2xl font-semibold text-gray-900">\${stats.data.current_month_invoices || 0}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-green-100">
                                        <i class="fas fa-euro-sign text-green-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Venituri luna aceasta</p>
                                        <p class="text-2xl font-semibold text-gray-900">\${formatCurrency(stats.data.current_month_revenue || 0)}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-yellow-100">
                                        <i class="fas fa-clock text-yellow-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Facturi restante</p>
                                        <p class="text-2xl font-semibold text-gray-900">\${stats.data.overdue_invoices || 0}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-full bg-purple-100">
                                        <i class="fas fa-users text-purple-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-600">Clienți activi</p>
                                        <p class="text-2xl font-semibold text-gray-900">\${stats.data.active_clients || 0}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Acțiuni rapide</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                                <button onclick="loadPage('invoices')" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-plus text-indigo-600 mr-3"></i>
                                    <span class="font-medium">Factură nouă</span>
                                </button>
                                <button onclick="loadPage('clients')" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-user-plus text-green-600 mr-3"></i>
                                    <span class="font-medium">Client nou</span>
                                </button>
                                <button onclick="loadPage('products')" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-box text-blue-600 mr-3"></i>
                                    <span class="font-medium">Produs nou</span>
                                </button>
                                <button onclick="loadPage('reports')" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-chart-bar text-purple-600 mr-3"></i>
                                    <span class="font-medium">Vezi rapoarte</span>
                                </button>
                            </div>
                        </div>

                        <!-- Recent Invoices -->
                        <div class="card">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Facturi recente</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Număr</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        \${(stats.data.recent_invoices || []).map(invoice => \`
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">\${invoice.series}\${invoice.number}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">\${invoice.client_name}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">\${formatDate(invoice.issue_date)}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">\${formatCurrency(invoice.total)}</td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full \${invoice.paid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                                        \${invoice.paid ? 'Plătită' : 'Neplătită'}
                                                    </span>
                                                </td>
                                            </tr>
                                        \`).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                \`;
            } catch (error) {
                throw error;
            }
        }

        // Invoice management
        async function loadInvoices() {
            if (typeof invoiceManager !== 'undefined') {
                await invoiceManager.render();
            } else {
                const content = document.getElementById('page-content');
                content.innerHTML = \`
                    <div class="card">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-semibold text-gray-900">Facturi</h2>
                            <button onclick="showCreateInvoiceModal()" class="btn-primary">
                                <i class="fas fa-plus mr-2"></i>
                                Factură nouă
                            </button>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Număr</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody id="invoices-tbody" class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            <i class="fas fa-spinner fa-spin mr-2"></i>
                                            Se încarcă facturile...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                \`;

                // Load invoices data
                try {
                    const response = await api.get('/invoices');
                    const invoices = response.data.invoices || [];
                    const tbody = document.getElementById('invoices-tbody');

                    if (invoices.length === 0) {
                        tbody.innerHTML = \`
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                    <i class="fas fa-file-invoice text-4xl mb-4 text-gray-300"></i>
                                    <p>Nu există facturi încă</p>
                                    <button onclick="showCreateInvoiceModal()" class="mt-2 btn-primary">
                                        Creează prima factură
                                    </button>
                                </td>
                            </tr>
                        \`;
                    } else {
                        tbody.innerHTML = invoices.map(invoice => \`
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    \${invoice.series}\${invoice.number}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    \${invoice.client_name}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    \${formatDate(invoice.issue_date)}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    \${formatCurrency(invoice.total)}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full \${invoice.paid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}">
                                        \${invoice.paid ? 'Plătită' : 'Neplătită'}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                    <button onclick="viewInvoice(\${invoice.id})" class="text-indigo-600 hover:text-indigo-900" title="Vezi">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button onclick="editInvoice(\${invoice.id})" class="text-blue-600 hover:text-blue-900" title="Editează">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button onclick="downloadInvoicePDF(\${invoice.id})" class="text-green-600 hover:text-green-900" title="PDF">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                </td>
                            </tr>
                        \`).join('');
                    }
                } catch (error) {
                    document.getElementById('invoices-tbody').innerHTML = \`
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-red-500">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                Eroare la încărcarea facturilor: \${error.message}
                            </td>
                        </tr>
                    \`;
                }
            }
        }

        // Helper functions for invoice actions
        async function showCreateInvoiceModal() {
            try {
                // Load required data first
                const [clientsRes, seriesRes, productsRes] = await Promise.all([
                    api.get('/clients'),
                    api.get('/settings/series'),
                    api.get('/products')
                ]);

                const clients = clientsRes.data || [];
                const series = seriesRes.data || [];
                const products = productsRes.data || [];

                // Store globally for later use
                window.invoiceData = { clients, series, products };
                window.invoiceProducts = products;

                // Create and show modal using DOM manipulation
                createInvoiceModal();

            } catch (error) {
                showToast('Eroare la încărcarea datelor pentru factură: ' + error.message, 'error');
            }
        }

        function createInvoiceModal() {
            // Remove existing modal if present
            const existingModal = document.getElementById('invoice-modal');
            if (existingModal) existingModal.remove();

            const modal = document.createElement('div');
            modal.id = 'invoice-modal';
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';

            const modalContent = document.createElement('div');
            modalContent.className = 'flex items-center justify-center min-h-screen p-4';

            const modalDialog = document.createElement('div');
            modalDialog.className = 'bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto';

            const modalBody = document.createElement('div');
            modalBody.className = 'p-6';

            // Header
            const header = document.createElement('div');
            header.className = 'flex justify-between items-center mb-6';
            
            const title = document.createElement('h3');
            title.className = 'text-lg font-medium text-gray-900';
            title.textContent = 'Factură nouă';
            
            const closeBtn = document.createElement('button');
            closeBtn.type = 'button';
            closeBtn.className = 'text-gray-400 hover:text-gray-600';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.addEventListener('click', () => modal.remove());
            
            header.appendChild(title);
            header.appendChild(closeBtn);

            // Form
            const form = document.createElement('form');
            form.id = 'invoice-form';
            form.className = 'space-y-6';

            // Basic fields container
            const basicFields = document.createElement('div');
            basicFields.className = 'grid grid-cols-1 md:grid-cols-2 gap-6';

            // Client field with "Add New" button
            const clientDiv = document.createElement('div');
            clientDiv.innerHTML = '<label class="block text-sm font-medium text-gray-700 mb-1">Client *</label>';
            
            const clientContainer = document.createElement('div');
            clientContainer.className = 'flex gap-2';
            
            const clientSelect = document.createElement('select');
            clientSelect.id = 'invoice-client';
            clientSelect.name = 'client_id';
            clientSelect.required = true;
            clientSelect.className = 'input-field flex-1';
            
            const clientDefaultOption = document.createElement('option');
            clientDefaultOption.value = '';
            clientDefaultOption.textContent = 'Selectează clientul';
            clientSelect.appendChild(clientDefaultOption);
            
            const addClientBtn = document.createElement('button');
            addClientBtn.type = 'button';
            addClientBtn.className = 'btn-secondary whitespace-nowrap';
            addClientBtn.innerHTML = '<i class="fas fa-plus mr-1"></i>Client nou';
            addClientBtn.addEventListener('click', () => showQuickClientModal());
            
            clientContainer.appendChild(clientSelect);
            clientContainer.appendChild(addClientBtn);
            clientDiv.appendChild(clientContainer);

            // Series field
            const seriesDiv = document.createElement('div');
            seriesDiv.innerHTML = '<label class="block text-sm font-medium text-gray-700 mb-1">Seria *</label>';
            const seriesSelect = document.createElement('select');
            seriesSelect.id = 'invoice-series';
            seriesSelect.name = 'series_id';
            seriesSelect.required = true;
            seriesSelect.className = 'input-field';
            
            const seriesDefaultOption = document.createElement('option');
            seriesDefaultOption.value = '';
            seriesDefaultOption.textContent = 'Selectează seria';
            seriesSelect.appendChild(seriesDefaultOption);
            
            seriesDiv.appendChild(seriesSelect);

            // Date fields
            const dateDiv = document.createElement('div');
            dateDiv.innerHTML = '<label class="block text-sm font-medium text-gray-700 mb-1">Data emiterii *</label><input type="date" id="invoice-date" name="issue_date" required class="input-field">';

            const dueDateDiv = document.createElement('div');
            dueDateDiv.innerHTML = '<label class="block text-sm font-medium text-gray-700 mb-1">Data scadenței</label><input type="date" id="invoice-due-date" name="due_date" class="input-field">';

            basicFields.appendChild(clientDiv);
            basicFields.appendChild(seriesDiv);
            basicFields.appendChild(dateDiv);
            basicFields.appendChild(dueDateDiv);

            // Items section
            const itemsSection = document.createElement('div');
            const itemsHeader = document.createElement('div');
            itemsHeader.className = 'flex justify-between items-center mb-4';
            
            const itemsTitle = document.createElement('h4');
            itemsTitle.className = 'text-lg font-medium text-gray-900';
            itemsTitle.textContent = 'Produse/Servicii';
            
            const addItemBtn = document.createElement('button');
            addItemBtn.type = 'button';
            addItemBtn.className = 'btn-secondary';
            addItemBtn.innerHTML = '<i class="fas fa-plus mr-2"></i>Adaugă produs';
            addItemBtn.addEventListener('click', addInvoiceItem);
            
            itemsHeader.appendChild(itemsTitle);
            itemsHeader.appendChild(addItemBtn);
            
            const itemsContainer = document.createElement('div');
            itemsContainer.id = 'invoice-items';
            itemsContainer.className = 'space-y-4';

            itemsSection.appendChild(itemsHeader);
            itemsSection.appendChild(itemsContainer);

            // Totals section
            const totalsSection = document.createElement('div');
            totalsSection.className = 'border-t pt-4';
            totalsSection.innerHTML = '<div class="flex justify-end"><div class="w-64 space-y-2"><div class="flex justify-between"><span class="text-sm text-gray-600">Subtotal:</span><span id="invoice-subtotal" class="text-sm font-medium">0.00 RON</span></div><div class="flex justify-between"><span class="text-sm text-gray-600">TVA:</span><span id="invoice-vat" class="text-sm font-medium">0.00 RON</span></div><div class="flex justify-between border-t pt-2"><span class="text-base font-medium">Total:</span><span id="invoice-total" class="text-base font-bold">0.00 RON</span></div></div></div>';

            // Notes section
            const notesSection = document.createElement('div');
            notesSection.innerHTML = '<label class="block text-sm font-medium text-gray-700 mb-1">Observații</label><textarea id="invoice-notes" name="notes" rows="3" class="input-field" placeholder="Observații pentru factură..."></textarea>';

            // Actions section
            const actionsSection = document.createElement('div');
            actionsSection.className = 'flex justify-end space-x-4 pt-6 border-t';
            
            const cancelBtn = document.createElement('button');
            cancelBtn.type = 'button';
            cancelBtn.className = 'btn-secondary';
            cancelBtn.textContent = 'Anulează';
            cancelBtn.addEventListener('click', () => modal.remove());
            
            const saveBtn = document.createElement('button');
            saveBtn.type = 'submit';
            saveBtn.className = 'btn-primary';
            saveBtn.innerHTML = '<span id="save-text">Salvează factura</span><i id="save-spinner" class="fas fa-spinner fa-spin hidden ml-2"></i>';
            
            actionsSection.appendChild(cancelBtn);
            actionsSection.appendChild(saveBtn);

            // Assemble form
            form.appendChild(basicFields);
            form.appendChild(itemsSection);
            form.appendChild(totalsSection);
            form.appendChild(notesSection);
            form.appendChild(actionsSection);

            // Assemble modal
            modalBody.appendChild(header);
            modalBody.appendChild(form);
            modalDialog.appendChild(modalBody);
            modalContent.appendChild(modalDialog);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Populate data and add listeners
            populateInvoiceModal();
            
            // Add form submit handler
            form.addEventListener('submit', saveInvoice);
        }

        function populateInvoiceModal() {
            const { clients, series } = window.invoiceData;

            // Populate clients
            const clientSelect = document.getElementById('invoice-client');
            clients.forEach(client => {
                const option = document.createElement('option');
                option.value = client.id;
                option.textContent = client.name;
                clientSelect.appendChild(option);
            });

            // Populate series
            const seriesSelect = document.getElementById('invoice-series');
            series.filter(s => s.type === 'invoice').forEach(s => {
                const option = document.createElement('option');
                option.value = s.id;
                option.textContent = s.series;
                seriesSelect.appendChild(option);
            });

            // Set today's date
            document.getElementById('invoice-date').value = new Date().toISOString().split('T')[0];

            // Add first item automatically
            addInvoiceItem();
        }

        async function viewInvoice(id) {
            try {
                const response = await api.get('/invoices/' + id);
                const invoice = response.data;
                
                // Remove existing modal if present
                const existingModal = document.getElementById('view-modal');
                if (existingModal) existingModal.remove();

                const modal = document.createElement('div');
                modal.id = 'view-modal';
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';

                const content = document.createElement('div');
                content.className = 'flex items-center justify-center min-h-screen p-4';

                const dialog = document.createElement('div');
                dialog.className = 'bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto p-6';

                // Header
                const header = document.createElement('div');
                header.className = 'flex justify-between items-center mb-6';
                const title = document.createElement('h3');
                title.className = 'text-lg font-medium text-gray-900';
                title.textContent = 'Factură ' + (invoice.series || '') + (invoice.number || '');
                const closeBtn = document.createElement('button');
                closeBtn.type = 'button';
                closeBtn.className = 'text-gray-400 hover:text-gray-600';
                closeBtn.innerHTML = '<i class="fas fa-times"></i>';
                closeBtn.addEventListener('click', () => modal.remove());
                header.appendChild(title);
                header.appendChild(closeBtn);

                // Details
                const details = document.createElement('div');
                details.className = 'space-y-6';
                
                const grid = document.createElement('div');
                grid.className = 'grid grid-cols-2 gap-6';
                
                const leftCol = document.createElement('div');
                leftCol.innerHTML = '<h4 class="font-medium text-gray-900 mb-2">Detalii factură</h4><p><strong>Client:</strong> ' + (invoice.client_name || '') + '</p><p><strong>Data emiterii:</strong> ' + formatDate(invoice.issue_date) + '</p><p><strong>Scadența:</strong> ' + (invoice.due_date ? formatDate(invoice.due_date) : '-') + '</p><p><strong>Status:</strong> <span class="px-2 py-1 text-xs rounded ' + (invoice.paid ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800') + '">' + (invoice.paid ? 'Plătită' : 'Neplătită') + '</span></p>';
                
                const rightCol = document.createElement('div');
                rightCol.innerHTML = '<h4 class="font-medium text-gray-900 mb-2">Total</h4><p class="text-2xl font-bold text-gray-900">' + formatCurrency(invoice.total || 0) + '</p>';

                grid.appendChild(leftCol);
                grid.appendChild(rightCol);

                // Actions
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'flex space-x-4';
                
                const pdfBtn = document.createElement('button');
                pdfBtn.className = 'btn-primary';
                pdfBtn.innerHTML = '<i class="fas fa-file-pdf mr-2"></i>Descarcă PDF';
                pdfBtn.addEventListener('click', () => downloadInvoicePDF(invoice.id));
                
                const emailBtn = document.createElement('button');
                emailBtn.className = 'btn-secondary';
                emailBtn.innerHTML = '<i class="fas fa-envelope mr-2"></i>Trimite email';
                emailBtn.addEventListener('click', () => sendInvoiceEmail(invoice.id));

                actionsDiv.appendChild(pdfBtn);
                actionsDiv.appendChild(emailBtn);

                details.appendChild(grid);
                details.appendChild(actionsDiv);

                dialog.appendChild(header);
                dialog.appendChild(details);
                content.appendChild(dialog);
                modal.appendChild(content);
                document.body.appendChild(modal);

            } catch (error) {
                showToast('Eroare la încărcarea facturii: ' + error.message, 'error');
            }
        }

        async function editInvoice(id) {
            try {
                const response = await api.get('/invoices/' + id);
                const invoice = response.data;
                
                // Show create modal and populate with existing data
                await showCreateInvoiceModal();
                
                setTimeout(() => {
                    // Populate form fields
                    const clientSelect = document.getElementById('invoice-client');
                    const seriesSelect = document.getElementById('invoice-series');
                    const dateInput = document.getElementById('invoice-date');
                    const dueDateInput = document.getElementById('invoice-due-date');
                    const notesInput = document.getElementById('invoice-notes');
                    
                    if (clientSelect && invoice.client_id) clientSelect.value = invoice.client_id;
                    if (seriesSelect && invoice.series_id) seriesSelect.value = invoice.series_id;
                    if (dateInput && invoice.issue_date) dateInput.value = invoice.issue_date.split('T')[0];
                    if (dueDateInput && invoice.due_date) dueDateInput.value = invoice.due_date.split('T')[0];
                    if (notesInput && invoice.notes) notesInput.value = invoice.notes;
                    
                    // Update modal title and form action
                    const title = document.querySelector('#invoice-modal h3');
                    if (title) title.textContent = 'Editează factura ' + (invoice.series || '') + (invoice.number || '');
                    
                    const form = document.getElementById('invoice-form');
                    if (form) form.dataset.editId = id;
                    
                    // Clear existing items and add invoice items
                    const itemsContainer = document.getElementById('invoice-items');
                    if (itemsContainer) {
                        itemsContainer.innerHTML = '';
                        
                        // Add items from invoice
                        if (invoice.items && invoice.items.length > 0) {
                            invoice.items.forEach((item, index) => {
                                addInvoiceItem();
                                
                                // Wait a bit for the item to be added, then populate it
                                setTimeout(() => {
                                    const itemElements = document.querySelectorAll('.invoice-item');
                                    const currentItem = itemElements[index];
                                    
                                    if (currentItem) {
                                        const productSelect = currentItem.querySelector('.product-select');
                                        const quantityInput = currentItem.querySelector('.quantity-input');
                                        const priceInput = currentItem.querySelector('.price-input');
                                        const vatInput = currentItem.querySelector('.vat-input');
                                        
                                        if (productSelect && item.product_id) productSelect.value = item.product_id;
                                        if (quantityInput) quantityInput.value = item.quantity;
                                        if (priceInput) priceInput.value = item.unit_price;
                                        if (vatInput) vatInput.value = item.vat_rate || 19;
                                        
                                        calculateInvoiceTotals();
                                    }
                                }, 100 * (index + 1));
                            });
                        } else {
                            addInvoiceItem(); // Add at least one empty item
                        }
                    }
                }, 200);
                
            } catch (error) {
                showToast('Eroare la încărcarea facturii pentru editare: ' + error.message, 'error');
            }
        }

        async function downloadInvoicePDF(id) {
            try {
                const response = await fetch(\`/api/invoices/\${id}/pdf\`, {
                    headers: {
                        'Authorization': \`Bearer \${app.token}\`
                    }
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = \`factura-\${id}.pdf\`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    showToast('PDF descărcat cu succes', 'success');
                } else {
                    throw new Error('Eroare la descărcarea PDF-ului');
                }
            } catch (error) {
                showToast(error.message, 'error');
            }
        }

        async function loadClients() {
            const content = document.getElementById('page-content');
            content.innerHTML = \`
                <div class="space-y-6">
                    <!-- Header -->
                    <div class="flex justify-between items-center">
                        <h2 class="text-2xl font-bold text-gray-900">Clienți</h2>
                        <button onclick="showCreateClientModal()" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Client nou
                        </button>
                    </div>

                    <!-- Clients Table -->
                    <div class="card">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nume</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CUI/CNP</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Telefon</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Oraș</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody id="clients-tbody" class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            <i class="fas fa-spinner fa-spin mr-2"></i>
                                            Se încarcă clienții...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Create/Edit Client Modal -->
                <div id="client-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden modal z-50">
                    <div class="flex items-center justify-center min-h-screen p-4">
                        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                            <div class="p-6">
                                <div class="flex justify-between items-center mb-6">
                                    <h3 id="client-modal-title" class="text-lg font-medium text-gray-900">Client nou</h3>
                                    <button onclick="closeClientModal()" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <form id="client-form" class="space-y-6">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Nume *</label>
                                            <input type="text" id="client-name" name="name" required class="input-field" placeholder="Numele clientului">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Tip client</label>
                                            <select id="client-type" name="type" class="input-field" onchange="toggleClientFields()">
                                                <option value="company">Companie</option>
                                                <option value="individual">Persoană fizică</option>
                                            </select>
                                        </div>
                                        <div id="cui-field">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">CUI</label>
                                            <input type="text" id="client-cui" name="cui" class="input-field" placeholder="RO12345678">
                                        </div>
                                        <div id="cnp-field" class="hidden">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">CNP</label>
                                            <input type="text" id="client-cnp" name="cnp" class="input-field" placeholder="1234567890123">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                            <input type="email" id="client-email" name="email" class="input-field" placeholder="<EMAIL>">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Telefon</label>
                                            <input type="tel" id="client-phone" name="phone" class="input-field" placeholder="+40123456789">
                                        </div>
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Adresă *</label>
                                            <input type="text" id="client-address" name="address" required class="input-field" placeholder="Strada, numărul">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Oraș *</label>
                                            <input type="text" id="client-city" name="city" required class="input-field" placeholder="București">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Județ *</label>
                                            <input type="text" id="client-county" name="county" required class="input-field" placeholder="București">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Cod poștal</label>
                                            <input type="text" id="client-postal" name="postal_code" class="input-field" placeholder="123456">
                                        </div>
                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" id="client-vat" name="vat_registered" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                                <span class="ml-2 text-sm text-gray-700">Plătitor de TVA</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex justify-end space-x-4 pt-6 border-t">
                                        <button type="button" onclick="closeClientModal()" class="btn-secondary">Anulează</button>
                                        <button type="submit" class="btn-primary">
                                            <span id="client-save-text">Salvează clientul</span>
                                            <i id="client-save-spinner" class="fas fa-spinner fa-spin hidden ml-2"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            \`;

            // Load clients data
            try {
                const response = await api.get('/clients');
                const clients = response.data || [];
                const tbody = document.getElementById('clients-tbody');

                if (clients.length === 0) {
                    tbody.innerHTML = \`
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                <i class="fas fa-users text-4xl mb-4 text-gray-300"></i>
                                <p>Nu există clienți încă</p>
                                <button onclick="showCreateClientModal()" class="mt-2 btn-primary">
                                    Adaugă primul client
                                </button>
                            </td>
                        </tr>
                    \`;
                } else {
                    tbody.innerHTML = clients.map(client => \`
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                \${client.name}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${client.cui || client.cnp || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${client.email || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${client.phone || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${client.city}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="editClient(\${client.id})" class="text-blue-600 hover:text-blue-900" title="Editează">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteClient(\${client.id})" class="text-red-600 hover:text-red-900" title="Șterge">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    \`).join('');
                }
            } catch (error) {
                document.getElementById('clients-tbody').innerHTML = \`
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-red-500">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Eroare la încărcarea clienților: \${error.message}
                        </td>
                    </tr>
                \`;
            }

            // Setup form submission
            document.getElementById('client-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await saveClient(e.target);
            });
        }

        // Client management functions
        function showCreateClientModal() {
            document.getElementById('client-modal-title').textContent = 'Client nou';
            document.getElementById('client-form').reset();
            document.getElementById('client-modal').classList.remove('hidden');
        }

        function closeClientModal() {
            document.getElementById('client-modal').classList.add('hidden');
        }

        function toggleClientFields() {
            const type = document.getElementById('client-type').value;
            const cuiField = document.getElementById('cui-field');
            const cnpField = document.getElementById('cnp-field');

            if (type === 'individual') {
                cuiField.classList.add('hidden');
                cnpField.classList.remove('hidden');
            } else {
                cuiField.classList.remove('hidden');
                cnpField.classList.add('hidden');
            }
        }

        async function saveClient(form) {
            const saveBtn = document.getElementById('client-save-text');
            const spinner = document.getElementById('client-save-spinner');

            saveBtn.textContent = 'Se salvează...';
            spinner.classList.remove('hidden');

            try {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);

                // Convert checkbox to boolean
                data.vat_registered = formData.has('vat_registered');
                data.is_individual = data.type === 'individual';

                await api.post('/clients', data);
                showToast('Clientul a fost salvat cu succes', 'success');
                closeClientModal();
                await loadClients();

            } catch (error) {
                showToast(error.message, 'error');
            } finally {
                saveBtn.textContent = 'Salvează clientul';
                spinner.classList.add('hidden');
            }
        }

        async function editClient(id) {
            try {
                const response = await api.get('/clients/' + id);
                const client = response.data;
                
                // Find existing modal and update it for editing
                showCreateClientModal();
                
                // Populate with existing data
                setTimeout(() => {
                    document.getElementById('client-name').value = client.name || '';
                    document.getElementById('client-cui').value = client.cui || '';
                    document.getElementById('client-email').value = client.email || '';
                    document.getElementById('client-phone').value = client.phone || '';
                    document.getElementById('client-address').value = client.address || '';
                    document.getElementById('client-city').value = client.city || '';
                    document.getElementById('client-county').value = client.county || '';
                    document.getElementById('client-postal').value = client.postal_code || '';
                    document.getElementById('client-individual').checked = client.is_individual || false;
                    document.getElementById('client-vat-registered').checked = client.vat_registered || false;
                    
                    // Update modal title and form action
                    document.querySelector('#client-modal h3').textContent = 'Editează client';
                    const form = document.getElementById('client-form');
                    form.dataset.editId = id;
                }, 100);
                
            } catch (error) {
                showToast('Eroare la încărcarea datelor clientului: ' + error.message, 'error');
            }
        }

        async function deleteClient(id) {
            if (confirm('Ești sigur că vrei să ștergi acest client?')) {
                try {
                    await api.delete(\`/clients/\${id}\`);
                    showToast('Clientul a fost șters', 'success');
                    await loadClients();
                } catch (error) {
                    showToast(error.message, 'error');
                }
            }
        }

        async function loadProducts() {
            const content = document.getElementById('page-content');
            content.innerHTML = \`
                <div class="space-y-6">
                    <!-- Header -->
                    <div class="flex justify-between items-center">
                        <h2 class="text-2xl font-bold text-gray-900">Produse și Servicii</h2>
                        <button onclick="showCreateProductModal()" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>
                            Produs nou
                        </button>
                    </div>

                    <!-- Products Table -->
                    <div class="card">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nume</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cod</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Preț</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TVA</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unitate</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tip</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acțiuni</th>
                                    </tr>
                                </thead>
                                <tbody id="products-tbody" class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            <i class="fas fa-spinner fa-spin mr-2"></i>
                                            Se încarcă produsele...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Create/Edit Product Modal -->
                <div id="product-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden modal z-50">
                    <div class="flex items-center justify-center min-h-screen p-4">
                        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                            <div class="p-6">
                                <div class="flex justify-between items-center mb-6">
                                    <h3 id="product-modal-title" class="text-lg font-medium text-gray-900">Produs nou</h3>
                                    <button onclick="closeProductModal()" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>

                                <form id="product-form" class="space-y-6">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Nume *</label>
                                            <input type="text" id="product-name" name="name" required class="input-field" placeholder="Numele produsului/serviciului">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Cod produs</label>
                                            <input type="text" id="product-code" name="code" class="input-field" placeholder="COD123">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Preț *</label>
                                            <input type="number" id="product-price" name="price" step="0.01" min="0" required class="input-field" placeholder="0.00">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Cota TVA *</label>
                                            <select id="product-vat" name="vat_rate" required class="input-field">
                                                <option value="0">0%</option>
                                                <option value="5">5%</option>
                                                <option value="9">9%</option>
                                                <option value="19" selected>19%</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Unitate de măsură</label>
                                            <select id="product-unit" name="unit" class="input-field">
                                                <option value="buc" selected>bucăți</option>
                                                <option value="kg">kilograme</option>
                                                <option value="m">metri</option>
                                                <option value="m2">metri pătrați</option>
                                                <option value="m3">metri cubi</option>
                                                <option value="l">litri</option>
                                                <option value="ore">ore</option>
                                                <option value="zile">zile</option>
                                                <option value="luni">luni</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Categorie</label>
                                            <input type="text" id="product-category" name="category" class="input-field" placeholder="Categoria produsului">
                                        </div>
                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" id="product-service" name="is_service" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                                <span class="ml-2 text-sm text-gray-700">Este serviciu</span>
                                            </label>
                                        </div>
                                        <div class="md:col-span-2">
                                            <label class="block text-sm font-medium text-gray-700 mb-1">Descriere</label>
                                            <textarea id="product-description" name="description" rows="3" class="input-field" placeholder="Descrierea produsului/serviciului..."></textarea>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="flex justify-end space-x-4 pt-6 border-t">
                                        <button type="button" onclick="closeProductModal()" class="btn-secondary">Anulează</button>
                                        <button type="submit" class="btn-primary">
                                            <span id="product-save-text">Salvează produsul</span>
                                            <i id="product-save-spinner" class="fas fa-spinner fa-spin hidden ml-2"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            \`;

            // Load products data
            try {
                const response = await api.get('/products');
                const products = response.data || [];
                const tbody = document.getElementById('products-tbody');

                if (products.length === 0) {
                    tbody.innerHTML = \`
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                <i class="fas fa-box text-4xl mb-4 text-gray-300"></i>
                                <p>Nu există produse încă</p>
                                <button onclick="showCreateProductModal()" class="mt-2 btn-primary">
                                    Adaugă primul produs
                                </button>
                            </td>
                        </tr>
                    \`;
                } else {
                    tbody.innerHTML = products.map(product => \`
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                \${product.name}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${product.code || '-'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                \${formatCurrency(product.price)}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${product.vat_rate}%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${product.unit}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full \${product.is_service ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}">
                                    \${product.is_service ? 'Serviciu' : 'Produs'}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="editProduct(\${product.id})" class="text-blue-600 hover:text-blue-900" title="Editează">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteProduct(\${product.id})" class="text-red-600 hover:text-red-900" title="Șterge">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    \`).join('');
                }
            } catch (error) {
                document.getElementById('products-tbody').innerHTML = \`
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-red-500">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Eroare la încărcarea produselor: \${error.message}
                        </td>
                    </tr>
                \`;
            }

            // Setup form submission
            document.getElementById('product-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await saveProduct(e.target);
            });
        }

        // Product management functions
        function showCreateProductModal() {
            document.getElementById('product-modal-title').textContent = 'Produs nou';
            document.getElementById('product-form').reset();
            document.getElementById('product-modal').classList.remove('hidden');
        }

        function closeProductModal() {
            document.getElementById('product-modal').classList.add('hidden');
        }

        async function saveProduct(form) {
            const saveBtn = document.getElementById('product-save-text');
            const spinner = document.getElementById('product-save-spinner');

            saveBtn.textContent = 'Se salvează...';
            spinner.classList.remove('hidden');

            try {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);

                // Convert checkbox to boolean and numbers
                data.is_service = formData.has('is_service');
                data.price = parseFloat(data.price);
                data.vat_rate = parseFloat(data.vat_rate);

                await api.post('/products', data);
                showToast('Produsul a fost salvat cu succes', 'success');
                closeProductModal();
                await loadProducts();

            } catch (error) {
                showToast(error.message, 'error');
            } finally {
                saveBtn.textContent = 'Salvează produsul';
                spinner.classList.add('hidden');
            }
        }

        async function editProduct(id) {
            try {
                const response = await api.get('/products/' + id);
                const product = response.data;
                
                // Show create modal and populate with existing data
                showCreateProductModal();
                
                setTimeout(() => {
                    document.getElementById('product-name').value = product.name || '';
                    document.getElementById('product-code').value = product.code || '';
                    document.getElementById('product-price').value = product.price || '';
                    document.getElementById('product-unit').value = product.unit || 'buc';
                    document.getElementById('product-vat-rate').value = product.vat_rate || '19';
                    document.getElementById('product-category').value = product.category || '';
                    document.getElementById('product-description').value = product.description || '';
                    document.getElementById('product-service').checked = product.is_service || false;
                    document.getElementById('product-active').checked = product.active !== false;
                    
                    // Update modal title and form action
                    document.querySelector('#product-modal h3').textContent = 'Editează produs';
                    const form = document.getElementById('product-form');
                    form.dataset.editId = id;
                }, 100);
                
            } catch (error) {
                showToast('Eroare la încărcarea datelor produsului: ' + error.message, 'error');
            }
        }

        async function deleteProduct(id) {
            if (confirm('Ești sigur că vrei să ștergi acest produs?')) {
                try {
                    await api.delete(\`/products/\${id}\`);
                    showToast('Produsul a fost șters', 'success');
                    await loadProducts();
                } catch (error) {
                    showToast(error.message, 'error');
                }
            }
        }

        async function loadPayments() {
            try {
                const response = await api.get('/payments');
                const payments = response.data || [];
                
                const content = document.getElementById('page-content');
                content.innerHTML = \`
                    <div class="space-y-6">
                        <!-- Header -->
                        <div class="flex justify-between items-center">
                            <h2 class="text-2xl font-bold text-gray-900">Plăți</h2>
                            <button onclick="showCreatePaymentModal()" class="btn-primary">
                                <i class="fas fa-plus mr-2"></i>
                                Înregistrează plată
                            </button>
                        </div>

                        <!-- Payments Table -->
                        <div class="card">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Data</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Factură</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sumă</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acțiuni</th>
                                        </tr>
                                    </thead>
                                    <tbody id="payments-tbody" class="bg-white divide-y divide-gray-200">
                                        \${payments.length === 0 ? \`
                                            <tr>
                                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                                    <i class="fas fa-credit-card text-4xl mb-4 text-gray-300"></i>
                                                    <p>Nu există plăți înregistrate</p>
                                                </td>
                                            </tr>
                                        \` : payments.map(payment => \`
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    \${formatDate(payment.payment_date)}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    \${payment.invoice_number || '-'}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    \${payment.client_name || '-'}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    \${formatCurrency(payment.amount)}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full \${
                                                        payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                        payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                        'bg-red-100 text-red-800'
                                                    }">
                                                        \${
                                                            payment.status === 'completed' ? 'Completă' :
                                                            payment.status === 'pending' ? 'În așteptare' :
                                                            'Eșuată'
                                                        }
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                                    <button onclick="viewPayment(\${payment.id})" class="text-indigo-600 hover:text-indigo-900" title="Vezi">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        \`).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                \`;
                
            } catch (error) {
                const content = document.getElementById('page-content');
                content.innerHTML = \`
                    <div class="card">
                        <div class="text-center py-8">
                            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Eroare la încărcarea plăților</h3>
                            <p class="text-gray-600">\${error.message}</p>
                            <button onclick="loadPayments()" class="mt-4 btn-primary">Încearcă din nou</button>
                        </div>
                    </div>
                \`;
            }
        }

        async function showCreatePaymentModal() {
            try {
                // Load invoices for payment selection
                const response = await api.get('/invoices?status=unpaid');
                const unpaidInvoices = response.data || [];
                
                // Create modal using DOM manipulation
                const modal = document.createElement('div');
                modal.id = 'payment-modal';
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';
                
                const content = document.createElement('div');
                content.className = 'flex items-center justify-center min-h-screen p-4';
                
                const dialog = document.createElement('div');
                dialog.className = 'bg-white rounded-lg shadow-xl max-w-lg w-full';
                
                const body = document.createElement('div');
                body.className = 'p-6';
                
                // Header
                const header = document.createElement('div');
                header.className = 'flex justify-between items-center mb-4';
                header.innerHTML = '<h3 class="text-lg font-medium text-gray-900">Înregistrează plată</h3><button type="button" class="text-gray-400 hover:text-gray-600 close-payment-modal"><i class="fas fa-times"></i></button>';
                
                // Form
                const form = document.createElement('form');
                form.className = 'space-y-4';
                form.innerHTML = '<div><label class="block text-sm font-medium text-gray-700 mb-1">Factură</label><select name="invoice_id" class="input-field" required><option value="">Selectează factura</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Sumă *</label><input type="number" name="amount" step="0.01" min="0" required class="input-field"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Data plății *</label><input type="date" name="payment_date" required class="input-field"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Metodă plată</label><select name="payment_method" class="input-field"><option value="transfer">Transfer bancar</option><option value="cash">Numerar</option><option value="card">Card</option><option value="check">Cec</option></select></div><div class="flex justify-end space-x-4 pt-4 border-t"><button type="button" class="btn-secondary cancel-payment">Anulează</button><button type="submit" class="btn-primary">Înregistrează plata</button></div>';
                
                // Populate invoices
                const invoiceSelect = form.querySelector('select[name="invoice_id"]');
                unpaidInvoices.forEach(invoice => {
                    const option = document.createElement('option');
                    option.value = invoice.id;
                    option.textContent = "Factura " + (invoice.series || '') + (invoice.number || '') + " - " + formatCurrency(invoice.total);
                    option.dataset.amount = invoice.total;
                    invoiceSelect.appendChild(option);
                });
                
                // Set today's date
                form.querySelector('input[name="payment_date"]').value = new Date().toISOString().split('T')[0];
                
                // Auto-fill amount when invoice is selected
                invoiceSelect.addEventListener('change', function() {
                    const option = this.options[this.selectedIndex];
                    if (option.dataset.amount) {
                        form.querySelector('input[name="amount"]').value = option.dataset.amount;
                    }
                });
                
                body.appendChild(header);
                body.appendChild(form);
                dialog.appendChild(body);
                content.appendChild(dialog);
                modal.appendChild(content);
                document.body.appendChild(modal);
                
                // Event listeners
                modal.querySelector('.close-payment-modal').addEventListener('click', () => modal.remove());
                modal.querySelector('.cancel-payment').addEventListener('click', () => modal.remove());
                
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    const formData = new FormData(e.target);
                    const data = Object.fromEntries(formData);
                    data.amount = parseFloat(data.amount);
                    
                    try {
                        await api.post('/payments', data);
                        showToast('Plata a fost înregistrată cu succes', 'success');
                        modal.remove();
                        await loadPayments();
                    } catch (error) {
                        showToast('Eroare la înregistrarea plății: ' + error.message, 'error');
                    }
                });
                
            } catch (error) {
                showToast('Eroare la încărcarea facturilor: ' + error.message, 'error');
            }
        }

        async function viewPayment(id) {
            try {
                const response = await api.get('/payments/' + id);
                const payment = response.data;
                
                // Create modal container
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';
                
                // Create modal content
                const content = document.createElement('div');
                content.className = 'flex items-center justify-center min-h-screen p-4';
                
                const dialog = document.createElement('div');
                dialog.className = 'bg-white rounded-lg shadow-xl max-w-lg w-full p-6';
                
                // Create header
                const header = document.createElement('div');
                header.className = 'flex justify-between items-center mb-4';
                
                const title = document.createElement('h3');
                title.className = 'text-lg font-medium text-gray-900';
                title.textContent = 'Detalii plată';
                
                const closeBtn = document.createElement('button');
                closeBtn.type = 'button';
                closeBtn.className = 'text-gray-400 hover:text-gray-600';
                closeBtn.addEventListener('click', function() {
                    modal.remove();
                });
                
                const closeIcon = document.createElement('i');
                closeIcon.className = 'fas fa-times';
                closeBtn.appendChild(closeIcon);
                
                header.appendChild(title);
                header.appendChild(closeBtn);
                
                // Create body
                const body = document.createElement('div');
                body.className = 'space-y-3';
                
                // Add payment details
                const amountP = document.createElement('p');
                const amountLabel = document.createElement('strong');
                amountLabel.textContent = 'Sumă:';
                amountP.appendChild(amountLabel);
                amountP.appendChild(document.createTextNode(' ' + formatCurrency(payment.amount)));
                
                const dateP = document.createElement('p');
                const dateLabel = document.createElement('strong');
                dateLabel.textContent = 'Data:';
                dateP.appendChild(dateLabel);
                dateP.appendChild(document.createTextNode(' ' + formatDate(payment.payment_date)));
                
                const methodP = document.createElement('p');
                const methodLabel = document.createElement('strong');
                methodLabel.textContent = 'Metodă:';
                methodP.appendChild(methodLabel);
                methodP.appendChild(document.createTextNode(' ' + (payment.payment_method || 'Transfer')));
                
                const statusP = document.createElement('p');
                const statusLabel = document.createElement('strong');
                statusLabel.textContent = 'Status:';
                statusP.appendChild(statusLabel);
                
                const statusSpan = document.createElement('span');
                statusSpan.className = 'px-2 py-1 text-xs rounded ' + 
                    (payment.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800');
                statusSpan.textContent = payment.status === 'completed' ? 'Completă' : 'În așteptare';
                statusP.appendChild(document.createTextNode(' '));
                statusP.appendChild(statusSpan);
                
                body.appendChild(amountP);
                body.appendChild(dateP);
                body.appendChild(methodP);
                body.appendChild(statusP);
                
                // Assemble modal
                dialog.appendChild(header);
                dialog.appendChild(body);
                content.appendChild(dialog);
                modal.appendChild(content);
                
                document.body.appendChild(modal);
            } catch (error) {
                showToast('Eroare la încărcarea plății: ' + error.message, 'error');
            }
        }

        async function loadReports() {
            const content = document.getElementById('page-content');
            content.innerHTML = \`
                <div class="space-y-6">
                    <!-- Header -->
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900">Rapoarte</h2>
                        <p class="text-gray-600">Generează rapoarte detaliate pentru afacerea ta</p>
                    </div>

                    <!-- Report Types -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Sales Report -->
                        <div class="card">
                            <div class="flex items-center mb-4">
                                <div class="p-3 rounded-full bg-blue-100">
                                    <i class="fas fa-chart-line text-blue-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">Raport Vânzări</h3>
                                    <p class="text-sm text-gray-600">Analiză detaliată a vânzărilor</p>
                                </div>
                            </div>
                            <button onclick="generateSalesReport()" class="w-full btn-primary">
                                <i class="fas fa-download mr-2"></i>
                                Generează raport
                            </button>
                        </div>

                        <!-- VAT Report -->
                        <div class="card">
                            <div class="flex items-center mb-4">
                                <div class="p-3 rounded-full bg-green-100">
                                    <i class="fas fa-percentage text-green-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">Raport TVA</h3>
                                    <p class="text-sm text-gray-600">Pentru declarația ANAF</p>
                                </div>
                            </div>
                            <button onclick="generateVATReport()" class="w-full btn-primary">
                                <i class="fas fa-download mr-2"></i>
                                Generează raport
                            </button>
                        </div>

                        <!-- Clients Report -->
                        <div class="card">
                            <div class="flex items-center mb-4">
                                <div class="p-3 rounded-full bg-purple-100">
                                    <i class="fas fa-users text-purple-600"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">Raport Clienți</h3>
                                    <p class="text-sm text-gray-600">Analiza comportamentului clienților</p>
                                </div>
                            </div>
                            <button onclick="generateClientsReport()" class="w-full btn-primary">
                                <i class="fas fa-download mr-2"></i>
                                Generează raport
                            </button>
                        </div>
                    </div>

                    <!-- Report Filters -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Filtre pentru rapoarte</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Perioada</label>
                                <select id="report-period" class="input-field">
                                    <option value="today">Astăzi</option>
                                    <option value="week">Săptămâna aceasta</option>
                                    <option value="month" selected>Luna aceasta</option>
                                    <option value="quarter">Trimestrul acesta</option>
                                    <option value="year">Anul acesta</option>
                                    <option value="custom">Personalizat</option>
                                </select>
                            </div>
                            <div id="custom-date-from" class="hidden">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data de la</label>
                                <input type="date" id="report-date-from" class="input-field">
                            </div>
                            <div id="custom-date-to" class="hidden">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Data până la</label>
                                <input type="date" id="report-date-to" class="input-field">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Format</label>
                                <select id="report-format" class="input-field">
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Reports -->
                    <div class="card">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Rapoarte recente</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-file-pdf text-red-500 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Raport Vânzări - Decembrie 2024</p>
                                        <p class="text-xs text-gray-500">Generat pe 15 Dec 2024</p>
                                    </div>
                                </div>
                                <button class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    <i class="fas fa-download mr-1"></i>
                                    Descarcă
                                </button>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-file-excel text-green-500 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Raport TVA - Q4 2024</p>
                                        <p class="text-xs text-gray-500">Generat pe 10 Dec 2024</p>
                                    </div>
                                </div>
                                <button class="text-indigo-600 hover:text-indigo-900 text-sm">
                                    <i class="fas fa-download mr-1"></i>
                                    Descarcă
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            \`;

            // Setup period change handler
            document.getElementById('report-period').addEventListener('change', function() {
                const customFromDiv = document.getElementById('custom-date-from');
                const customToDiv = document.getElementById('custom-date-to');

                if (this.value === 'custom') {
                    customFromDiv.classList.remove('hidden');
                    customToDiv.classList.remove('hidden');
                } else {
                    customFromDiv.classList.add('hidden');
                    customToDiv.classList.add('hidden');
                }
            });
        }

        // Report generation functions
        async function generateSalesReport() {
            const period = document.getElementById('report-period').value;
            const format = document.getElementById('report-format').value;

            try {
                showToast('Se generează raportul de vânzări...', 'info');

                let params = \`period=\${period}&format=\${format}\`;

                if (period === 'custom') {
                    const dateFrom = document.getElementById('report-date-from').value;
                    const dateTo = document.getElementById('report-date-to').value;
                    if (dateFrom && dateTo) {
                        params += \`&start_date=\${dateFrom}&end_date=\${dateTo}\`;
                    }
                }

                const response = await fetch(\`/api/reports/sales?\${params}\`, {
                    headers: {
                        'Authorization': \`Bearer \${app.token}\`
                    }
                });

                if (response.ok) {
                    if (format === 'pdf' || format === 'excel' || format === 'csv') {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = \`raport-vanzari.\${format === 'excel' ? 'xlsx' : format}\`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        showToast('Raportul a fost descărcat cu succes', 'success');
                    } else {
                        const data = await response.json();
                        console.log('Sales report data:', data);
                        showToast('Raportul a fost generat cu succes', 'success');
                    }
                } else {
                    throw new Error('Eroare la generarea raportului');
                }
            } catch (error) {
                showToast(error.message, 'error');
            }
        }

        async function generateVATReport() {
            const period = document.getElementById('report-period').value;
            const format = document.getElementById('report-format').value;

            try {
                showToast('Se generează raportul TVA...', 'info');

                let params = \`period=\${period}&format=\${format}\`;

                if (period === 'custom') {
                    const dateFrom = document.getElementById('report-date-from').value;
                    const dateTo = document.getElementById('report-date-to').value;
                    if (dateFrom && dateTo) {
                        params += \`&start_date=\${dateFrom}&end_date=\${dateTo}\`;
                    }
                }

                const response = await fetch(\`/api/reports/vat?\${params}\`, {
                    headers: {
                        'Authorization': \`Bearer \${app.token}\`
                    }
                });

                if (response.ok) {
                    if (format === 'pdf' || format === 'excel' || format === 'csv') {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = \`raport-tva.\${format === 'excel' ? 'xlsx' : format}\`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        showToast('Raportul TVA a fost descărcat cu succes', 'success');
                    } else {
                        const data = await response.json();
                        console.log('VAT report data:', data);
                        showToast('Raportul TVA a fost generat cu succes', 'success');
                    }
                } else {
                    throw new Error('Eroare la generarea raportului TVA');
                }
            } catch (error) {
                showToast(error.message, 'error');
            }
        }

        async function generateClientsReport() {
            const period = document.getElementById('report-period').value;
            const format = document.getElementById('report-format').value;

            try {
                showToast('Se generează raportul clienți...', 'info');

                let params = \`period=\${period}&format=\${format}\`;

                if (period === 'custom') {
                    const dateFrom = document.getElementById('report-date-from').value;
                    const dateTo = document.getElementById('report-date-to').value;
                    if (dateFrom && dateTo) {
                        params += \`&start_date=\${dateFrom}&end_date=\${dateTo}\`;
                    }
                }

                const response = await fetch(\`/api/reports/clients?\${params}\`, {
                    headers: {
                        'Authorization': \`Bearer \${app.token}\`
                    }
                });

                if (response.ok) {
                    if (format === 'pdf' || format === 'excel' || format === 'csv') {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = \`raport-clienti.\${format === 'excel' ? 'xlsx' : format}\`;
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                        showToast('Raportul clienți a fost descărcat cu succes', 'success');
                    } else {
                        const data = await response.json();
                        console.log('Clients report data:', data);
                        showToast('Raportul clienți a fost generat cu succes', 'success');
                    }
                } else {
                    throw new Error('Eroare la generarea raportului clienți');
                }
            } catch (error) {
                showToast(error.message, 'error');
            }
        }

        async function loadRecurring() {
            try {
                const response = await api.get('/invoices/recurring');
                const recurringInvoices = response.data || [];
                
                const content = document.getElementById('page-content');
                content.innerHTML = \`
                    <div class="space-y-6">
                        <!-- Header -->
                        <div class="flex justify-between items-center">
                            <h2 class="text-2xl font-bold text-gray-900">Facturi recurente</h2>
                            <button onclick="showCreateRecurringModal()" class="btn-primary">
                                <i class="fas fa-plus mr-2"></i>
                                Factură recurentă nouă
                            </button>
                        </div>

                        <!-- Recurring Invoices Table -->
                        <div class="card">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Template</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Frecvență</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acțiuni</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        \${recurringInvoices.length === 0 ? \`
                                            <tr>
                                                <td colspan="5" class="px-6 py-8 text-center text-gray-500">
                                                    <i class="fas fa-sync-alt text-4xl mb-4 text-gray-300"></i>
                                                    <p>Nu există facturi recurente</p>
                                                    <button onclick="showCreateRecurringModal()" class="mt-2 btn-primary">
                                                        Creează prima factură recurentă
                                                    </button>
                                                </td>
                                            </tr>
                                        \` : recurringInvoices.map(recurring => \`
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    \${recurring.template_name}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    \${recurring.client_name}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    \${recurring.frequency === 'monthly' ? 'Lunar' :
                                                      recurring.frequency === 'weekly' ? 'Săptămânal' :
                                                      recurring.frequency === 'quarterly' ? 'Trimestrial' : 'Anual'}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full \${recurring.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                                        \${recurring.is_active ? 'Activă' : 'Inactivă'}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                                    <button onclick="viewRecurring(\${recurring.id})" class="text-indigo-600 hover:text-indigo-900" title="Vezi">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        \`).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                \`;
                
            } catch (error) {
                const content = document.getElementById('page-content');
                content.innerHTML = \`
                    <div class="card">
                        <div class="text-center py-8">
                            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Eroare la încărcarea facturilor recurente</h3>
                            <p class="text-gray-600">\${error.message}</p>
                            <button onclick="loadRecurring()" class="mt-4 btn-primary">Încearcă din nou</button>
                        </div>
                    </div>
                \`;
            }
        }

        async function showCreateRecurringModal() {
            try {
                // Load required data
                const [clientsRes, seriesRes] = await Promise.all([
                    api.get('/clients'),
                    api.get('/settings/series')
                ]);

                const clients = clientsRes.data || [];
                const series = seriesRes.data || [];
                
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';
                
                const content = document.createElement('div');
                content.className = 'flex items-center justify-center min-h-screen p-4';
                
                const dialog = document.createElement('div');
                dialog.className = 'bg-white rounded-lg shadow-xl max-w-lg w-full p-6';
                
                dialog.innerHTML = '<div class="flex justify-between items-center mb-4"><h3 class="text-lg font-medium text-gray-900">Factură recurentă nouă</h3><button type="button" class="text-gray-400 hover:text-gray-600 close-recurring-modal"><i class="fas fa-times"></i></button></div><form class="space-y-4"><div><label class="block text-sm font-medium text-gray-700 mb-1">Nume template *</label><input type="text" name="template_name" required class="input-field" placeholder="Ex: Abonament lunar"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Client *</label><select name="client_id" required class="input-field"><option value="">Selectează clientul</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Seria *</label><select name="series_id" required class="input-field"><option value="">Selectează seria</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Frecvență *</label><select name="frequency" required class="input-field"><option value="weekly">Săptămânal</option><option value="monthly" selected>Lunar</option><option value="quarterly">Trimestrial</option><option value="yearly">Anual</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Prima generare</label><input type="date" name="start_date" required class="input-field"></div><div class="flex justify-end space-x-4 pt-4 border-t"><button type="button" class="btn-secondary cancel-recurring">Anulează</button><button type="submit" class="btn-primary">Creează template</button></div></form>';
                
                // Populate selects
                const clientSelect = dialog.querySelector('select[name="client_id"]');
                clients.forEach(client => {
                    const option = document.createElement('option');
                    option.value = client.id;
                    option.textContent = client.name;
                    clientSelect.appendChild(option);
                });
                
                const seriesSelect = dialog.querySelector('select[name="series_id"]');
                series.filter(s => s.type === 'invoice').forEach(s => {
                    const option = document.createElement('option');
                    option.value = s.id;
                    option.textContent = s.series;
                    seriesSelect.appendChild(option);
                });
                
                // Set next month as default start date
                const nextMonth = new Date();
                nextMonth.setMonth(nextMonth.getMonth() + 1);
                dialog.querySelector('input[name="start_date"]').value = nextMonth.toISOString().split('T')[0];
                
                content.appendChild(dialog);
                modal.appendChild(content);
                document.body.appendChild(modal);
                
                // Event listeners
                dialog.querySelector('.close-recurring-modal').addEventListener('click', () => modal.remove());
                dialog.querySelector('.cancel-recurring').addEventListener('click', () => modal.remove());
                
                dialog.querySelector('form').addEventListener('submit', async (e) => {
                    e.preventDefault();
                    const formData = new FormData(e.target);
                    const data = Object.fromEntries(formData);
                    
                    try {
                        await api.post('/invoices/recurring', data);
                        showToast('Template de factură recurentă creat cu succes', 'success');
                        modal.remove();
                        await loadRecurring();
                    } catch (error) {
                        showToast('Eroare la crearea template-ului: ' + error.message, 'error');
                    }
                });
                
            } catch (error) {
                showToast('Eroare la încărcarea datelor: ' + error.message, 'error');
            }
        }

        async function viewRecurring(id) {
            try {
                const response = await api.get('/invoices/recurring/' + id);
                const recurring = response.data;
                
                // Create modal container
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';
                
                // Create modal content
                const content = document.createElement('div');
                content.className = 'flex items-center justify-center min-h-screen p-4';
                
                const dialog = document.createElement('div');
                dialog.className = 'bg-white rounded-lg shadow-xl max-w-lg w-full p-6';
                
                // Create header
                const header = document.createElement('div');
                header.className = 'flex justify-between items-center mb-4';
                
                const title = document.createElement('h3');
                title.className = 'text-lg font-medium text-gray-900';
                title.textContent = 'Detalii factură recurentă';
                
                const closeBtn = document.createElement('button');
                closeBtn.type = 'button';
                closeBtn.className = 'text-gray-400 hover:text-gray-600';
                closeBtn.addEventListener('click', function() {
                    modal.remove();
                });
                
                const closeIcon = document.createElement('i');
                closeIcon.className = 'fas fa-times';
                closeBtn.appendChild(closeIcon);
                
                header.appendChild(title);
                header.appendChild(closeBtn);
                
                // Create body
                const body = document.createElement('div');
                body.className = 'space-y-3';
                
                // Add recurring invoice details
                const templateP = document.createElement('p');
                const templateLabel = document.createElement('strong');
                templateLabel.textContent = 'Template:';
                templateP.appendChild(templateLabel);
                templateP.appendChild(document.createTextNode(' ' + (recurring.template_name || '')));
                
                const clientP = document.createElement('p');
                const clientLabel = document.createElement('strong');
                clientLabel.textContent = 'Client:';
                clientP.appendChild(clientLabel);
                clientP.appendChild(document.createTextNode(' ' + (recurring.client_name || '')));
                
                const frequencyP = document.createElement('p');
                const frequencyLabel = document.createElement('strong');
                frequencyLabel.textContent = 'Frecvență:';
                frequencyP.appendChild(frequencyLabel);
                
                let frequencyText = '';
                if (recurring.frequency === 'monthly') frequencyText = 'Lunar';
                else if (recurring.frequency === 'weekly') frequencyText = 'Săptămânal';
                else if (recurring.frequency === 'quarterly') frequencyText = 'Trimestrial';
                else frequencyText = 'Anual';
                
                frequencyP.appendChild(document.createTextNode(' ' + frequencyText));
                
                const nextGenP = document.createElement('p');
                const nextGenLabel = document.createElement('strong');
                nextGenLabel.textContent = 'Următoarea generare:';
                nextGenP.appendChild(nextGenLabel);
                nextGenP.appendChild(document.createTextNode(' ' + formatDate(recurring.next_generation_date)));
                
                const statusP = document.createElement('p');
                const statusLabel = document.createElement('strong');
                statusLabel.textContent = 'Status:';
                statusP.appendChild(statusLabel);
                
                const statusSpan = document.createElement('span');
                statusSpan.className = 'px-2 py-1 text-xs rounded ' + 
                    (recurring.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800');
                statusSpan.textContent = recurring.is_active ? 'Activă' : 'Inactivă';
                statusP.appendChild(document.createTextNode(' '));
                statusP.appendChild(statusSpan);
                
                body.appendChild(templateP);
                body.appendChild(clientP);
                body.appendChild(frequencyP);
                body.appendChild(nextGenP);
                body.appendChild(statusP);
                
                // Create footer
                const footer = document.createElement('div');
                footer.className = 'flex justify-end space-x-2 pt-4 border-t';
                
                const toggleBtn = document.createElement('button');
                toggleBtn.className = 'btn-secondary';
                toggleBtn.textContent = recurring.is_active ? 'Dezactivează' : 'Activează';
                toggleBtn.addEventListener('click', function() {
                    toggleRecurring(recurring.id, !recurring.is_active);
                    modal.remove();
                });
                
                footer.appendChild(toggleBtn);
                
                // Assemble modal
                dialog.appendChild(header);
                dialog.appendChild(body);
                dialog.appendChild(footer);
                content.appendChild(dialog);
                modal.appendChild(content);
                
                document.body.appendChild(modal);
                
            } catch (error) {
                showToast('Eroare la încărcarea detaliilor: ' + error.message, 'error');
            }
        }

        async function toggleRecurring(id, activate) {
            try {
                await api.patch('/invoices/recurring/' + id, { is_active: activate });
                showToast('Statutul a fost actualizat cu succes', 'success');
                await loadRecurring();
            } catch (error) {
                showToast('Eroare la actualizarea statusului: ' + error.message, 'error');
            }
        }

        async function loadSettings() {
            try {
                const response = await api.get('/settings');
                const settings = response.data || {};
                
                const content = document.getElementById('page-content');
                content.innerHTML = \`
                    <div class="space-y-6">
                        <!-- Header -->
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">Setări</h2>
                            <p class="text-gray-600">Configurează setările aplicației tale</p>
                        </div>

                        <!-- Company Settings -->
                        <div class="card">
                            <div class="border-b border-gray-200 pb-4 mb-6">
                                <h3 class="text-lg font-medium text-gray-900">Informații companie</h3>
                                <p class="text-sm text-gray-600">Configurează datele companiei tale</p>
                            </div>
                            
                            <form id="company-settings-form" class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Numele companiei</label>
                                        <input type="text" name="company_name" value="\${settings.company_name || ''}" class="input-field">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">CUI/CIF</label>
                                        <input type="text" name="cui" value="\${settings.cui || ''}" class="input-field">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Nr. Reg. Com.</label>
                                        <input type="text" name="reg_com" value="\${settings.reg_com || ''}" class="input-field">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                        <input type="email" name="email" value="\${settings.email || ''}" class="input-field">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Telefon</label>
                                        <input type="text" name="phone" value="\${settings.phone || ''}" class="input-field">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                                        <input type="url" name="website" value="\${settings.website || ''}" class="input-field">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Adresa</label>
                                    <textarea name="address" rows="3" class="input-field">\${settings.address || ''}</textarea>
                                </div>
                                
                                <div class="flex justify-end">
                                    <button type="submit" class="btn-primary">
                                        <span>Salvează</span>
                                        <i class="fas fa-spinner fa-spin hidden ml-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Invoice Series -->
                        <div class="card">
                            <div class="border-b border-gray-200 pb-4 mb-6">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">Serii facturi</h3>
                                        <p class="text-sm text-gray-600">Gestionează seriile pentru facturi și alte documente</p>
                                    </div>
                                    <button onclick="showCreateSeriesModal()" class="btn-secondary">
                                        <i class="fas fa-plus mr-2"></i>
                                        Adaugă serie
                                    </button>
                                </div>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Seria</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tip</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Număr curent</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acțiuni</th>
                                        </tr>
                                    </thead>
                                    <tbody id="series-tbody" class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                                                Se încarcă seriile...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- System Settings -->
                        <div class="card">
                            <div class="border-b border-gray-200 pb-4 mb-6">
                                <h3 class="text-lg font-medium text-gray-900">Setări sistem</h3>
                                <p class="text-sm text-gray-600">Configurează comportamentul aplicației</p>
                            </div>
                            
                            <form id="system-settings-form" class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Moneda implicită</label>
                                        <select name="default_currency" class="input-field">
                                            <option value="RON" \${settings.default_currency === 'RON' ? 'selected' : ''}>RON</option>
                                            <option value="EUR" \${settings.default_currency === 'EUR' ? 'selected' : ''}>EUR</option>
                                            <option value="USD" \${settings.default_currency === 'USD' ? 'selected' : ''}>USD</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">TVA implicită (%)</label>
                                        <select name="default_vat_rate" class="input-field">
                                            <option value="19" \${settings.default_vat_rate === 19 ? 'selected' : ''}>19%</option>
                                            <option value="9" \${settings.default_vat_rate === 9 ? 'selected' : ''}>9%</option>
                                            <option value="5" \${settings.default_vat_rate === 5 ? 'selected' : ''}>5%</option>
                                            <option value="0" \${settings.default_vat_rate === 0 ? 'selected' : ''}>0%</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" name="auto_send_emails" \${settings.auto_send_emails ? 'checked' : ''} class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <label class="ml-2 text-sm text-gray-700">Trimite automat email-uri pentru facturi</label>
                                </div>
                                
                                <div class="flex justify-end">
                                    <button type="submit" class="btn-primary">
                                        <span>Salvează</span>
                                        <i class="fas fa-spinner fa-spin hidden ml-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                \`;

                // Load series data
                loadSeries();

                // Setup form handlers
                setupSettingsForms();
                
            } catch (error) {
                const content = document.getElementById('page-content');
                content.innerHTML = \`
                    <div class="card">
                        <div class="text-center py-8">
                            <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">Eroare la încărcarea setărilor</h3>
                            <p class="text-gray-600">\${error.message}</p>
                            <button onclick="loadSettings()" class="mt-4 btn-primary">Încearcă din nou</button>
                        </div>
                    </div>
                \`;
            }
        }

        function setupSettingsForms() {
            // Company settings form
            document.getElementById('company-settings-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);
                
                try {
                    await api.post('/settings/company', data);
                    showToast('Setările companiei au fost salvate', 'success');
                } catch (error) {
                    showToast('Eroare la salvarea setărilor: ' + error.message, 'error');
                }
            });

            // System settings form
            document.getElementById('system-settings-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);
                data.auto_send_emails = formData.has('auto_send_emails');
                
                try {
                    await api.post('/settings/system', data);
                    showToast('Setările sistemului au fost salvate', 'success');
                } catch (error) {
                    showToast('Eroare la salvarea setărilor: ' + error.message, 'error');
                }
            });
        }

        async function loadSeries() {
            try {
                const response = await api.get('/settings/series');
                const series = response.data || [];
                const tbody = document.getElementById('series-tbody');
                
                if (series.length === 0) {
                    tbody.innerHTML = \`
                        <tr>
                            <td colspan="4" class="px-6 py-8 text-center text-gray-500">
                                <p>Nu există serii configurate</p>
                                <button onclick="showCreateSeriesModal()" class="mt-2 btn-primary">
                                    Adaugă prima serie
                                </button>
                            </td>
                        </tr>
                    \`;
                } else {
                    tbody.innerHTML = series.map(s => \`
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                \${s.series}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${s.type === 'invoice' ? 'Factură' :
                                  s.type === 'receipt' ? 'Chitanță' :
                                  s.type === 'proforma' ? 'Proformă' : s.type}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                \${s.current_number || 1}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="editSeries(\${s.id})" class="text-blue-600 hover:text-blue-900" title="Editează">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="deleteSeries(\${s.id})" class="text-red-600 hover:text-red-900" title="Șterge">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    \`).join('');
                }
            } catch (error) {
                console.error('Error loading series:', error);
            }
        }

        function showCreateSeriesModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';
            
            const content = document.createElement('div');
            content.className = 'flex items-center justify-center min-h-screen p-4';
            
            const dialog = document.createElement('div');
            dialog.className = 'bg-white rounded-lg shadow-xl max-w-lg w-full p-6';
            
            dialog.innerHTML = '<div class="flex justify-between items-center mb-4"><h3 class="text-lg font-medium text-gray-900">Serie nouă</h3><button type="button" class="text-gray-400 hover:text-gray-600 close-series-modal"><i class="fas fa-times"></i></button></div><form class="space-y-4"><div><label class="block text-sm font-medium text-gray-700 mb-1">Seria *</label><input type="text" name="series" required class="input-field" placeholder="Ex: INV" maxlength="10"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Tip document *</label><select name="type" required class="input-field"><option value="invoice">Factură</option><option value="proforma">Proformă</option><option value="receipt">Chitanță</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Număr de pornire</label><input type="number" name="start_number" value="1" min="1" class="input-field"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Descriere</label><input type="text" name="description" class="input-field" placeholder="Descriere opțională"></div><div class="flex justify-end space-x-4 pt-4 border-t"><button type="button" class="btn-secondary cancel-series">Anulează</button><button type="submit" class="btn-primary">Creează seria</button></div></form>';
            
            content.appendChild(dialog);
            modal.appendChild(content);
            document.body.appendChild(modal);
            
            // Event listeners
            dialog.querySelector('.close-series-modal').addEventListener('click', () => modal.remove());
            dialog.querySelector('.cancel-series').addEventListener('click', () => modal.remove());
            
            dialog.querySelector('form').addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);
                data.start_number = parseInt(data.start_number) || 1;
                
                try {
                    await api.post('/settings/series', data);
                    showToast('Seria a fost creată cu succes', 'success');
                    modal.remove();
                    await loadSeries();
                } catch (error) {
                    showToast('Eroare la crearea seriei: ' + error.message, 'error');
                }
            });
        }

        async function editSeries(id) {
            try {
                const response = await api.get('/settings/series/' + id);
                const series = response.data;
                
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';
                
                const content = document.createElement('div');
                content.className = 'flex items-center justify-center min-h-screen p-4';
                
                const dialog = document.createElement('div');
                dialog.className = 'bg-white rounded-lg shadow-xl max-w-lg w-full p-6';
                
                dialog.innerHTML = '<div class="flex justify-between items-center mb-4"><h3 class="text-lg font-medium text-gray-900">Editează seria</h3><button type="button" class="text-gray-400 hover:text-gray-600 close-edit-series-modal"><i class="fas fa-times"></i></button></div><form class="space-y-4"><div><label class="block text-sm font-medium text-gray-700 mb-1">Seria *</label><input type="text" name="series" required class="input-field" maxlength="10"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Tip document *</label><select name="type" required class="input-field"><option value="invoice">Factură</option><option value="proforma">Proformă</option><option value="receipt">Chitanță</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Număr curent</label><input type="number" name="current_number" min="1" class="input-field"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Descriere</label><input type="text" name="description" class="input-field"></div><div class="flex justify-end space-x-4 pt-4 border-t"><button type="button" class="btn-secondary cancel-edit-series">Anulează</button><button type="submit" class="btn-primary">Actualizează seria</button></div></form>';
                
                // Populate form with existing data
                const form = dialog.querySelector('form');
                form.querySelector('input[name="series"]').value = series.series || '';
                form.querySelector('select[name="type"]').value = series.type || 'invoice';
                form.querySelector('input[name="current_number"]').value = series.current_number || 1;
                form.querySelector('input[name="description"]').value = series.description || '';
                
                content.appendChild(dialog);
                modal.appendChild(content);
                document.body.appendChild(modal);
                
                // Event listeners
                dialog.querySelector('.close-edit-series-modal').addEventListener('click', () => modal.remove());
                dialog.querySelector('.cancel-edit-series').addEventListener('click', () => modal.remove());
                
                form.addEventListener('submit', async (e) => {
                    e.preventDefault();
                    const formData = new FormData(e.target);
                    const data = Object.fromEntries(formData);
                    data.current_number = parseInt(data.current_number) || 1;
                    
                    try {
                        await api.put('/settings/series/' + id, data);
                        showToast('Seria a fost actualizată cu succes', 'success');
                        modal.remove();
                        await loadSeries();
                    } catch (error) {
                        showToast('Eroare la actualizarea seriei: ' + error.message, 'error');
                    }
                });
                
            } catch (error) {
                showToast('Eroare la încărcarea seriei: ' + error.message, 'error');
            }
        }

        async function deleteSeries(id) {
            if (confirm('Ești sigur că vrei să ștergi această serie?')) {
                try {
                    await api.delete('/settings/series/' + id);
                    showToast('Seria a fost ștearsă cu succes', 'success');
                    await loadSeries();
                } catch (error) {
                    showToast('Eroare la ștergerea seriei: ' + error.message, 'error');
                }
            }
        }

        // Additional helper functions
        async function downloadInvoicePDF(invoiceId) {
            try {
                const response = await fetch('/api/invoices/' + invoiceId + '/pdf', {
                    headers: {
                        'Authorization': 'Bearer ' + app.token
                    }
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'factura-' + invoiceId + '.pdf';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    showToast('PDF-ul a fost descărcat cu succes', 'success');
                } else {
                    throw new Error('Eroare la descărcarea PDF-ului');
                }
            } catch (error) {
                showToast('Eroare la descărcarea PDF-ului: ' + error.message, 'error');
            }
        }

        async function sendInvoiceEmail(invoiceId) {
            try {
                await api.post('/invoices/' + invoiceId + '/send-email');
                showToast('Email-ul a fost trimis cu succes', 'success');
            } catch (error) {
                showToast('Eroare la trimiterea email-ului: ' + error.message, 'error');
            }
        }

        function addInvoiceItem() {
            const container = document.getElementById('invoice-items');
            if (!container) return;
            
            const itemIndex = container.children.length;
            
            const itemDiv = document.createElement('div');
            itemDiv.className = 'invoice-item grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border rounded-lg';
            
            // Build the item structure first
            itemDiv.innerHTML = '<div><label class="block text-sm font-medium text-gray-700 mb-1">Produs</label><div class="flex gap-2"><select name="items[' + itemIndex + '][product_id]" class="input-field product-select flex-1"><option value="">Selectează produs</option></select><button type="button" class="btn-secondary whitespace-nowrap add-product-btn"><i class="fas fa-plus mr-1"></i>Nou</button></div></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Cantitate</label><input type="number" name="items[' + itemIndex + '][quantity]" value="1" min="0.01" step="0.01" class="input-field quantity-input"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Preț unitar</label><input type="number" name="items[' + itemIndex + '][unit_price]" min="0" step="0.01" class="input-field price-input"></div><div><label class="block text-sm font-medium text-gray-700 mb-1">TVA (%)</label><select name="items[' + itemIndex + '][vat_rate]" class="input-field vat-input"><option value="0">0%</option><option value="5">5%</option><option value="9">9%</option><option value="19" selected>19%</option></select></div><div><label class="block text-sm font-medium text-gray-700 mb-1">Total</label><input type="text" class="input-field item-total" readonly></div><div class="flex items-end"><button type="button" onclick="removeInvoiceItem(this)" class="btn-danger"><i class="fas fa-trash"></i></button></div>';
            
            // Get the product select and add products
            const productSelect = itemDiv.querySelector('.product-select');
            
            // Add products to select
            (window.invoiceProducts || []).forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = product.name;
                option.dataset.price = product.price;
                option.dataset.vat = product.vat_rate;
                productSelect.appendChild(option);
            });
            
            // Add event listener for "Add New Product" button
            const addProductBtn = itemDiv.querySelector('.add-product-btn');
            addProductBtn.addEventListener('click', () => showQuickProductModal(productSelect));
            
            container.appendChild(itemDiv);
            
            // Add event listeners for calculations
            const inputs = itemDiv.querySelectorAll('.quantity-input, .price-input, .vat-input');
            inputs.forEach(input => {
                input.addEventListener('input', calculateInvoiceTotals);
            });
            
            productSelect.addEventListener('change', function() {
                const option = this.options[this.selectedIndex];
                if (option.value) {
                    itemDiv.querySelector('.price-input').value = option.dataset.price || '';
                    itemDiv.querySelector('.vat-input').value = option.dataset.vat || '19';
                    calculateInvoiceTotals();
                }
            });
        }

        function removeInvoiceItem(button) {
            button.closest('.invoice-item').remove();
            calculateInvoiceTotals();
        }

        function calculateInvoiceTotals() {
            const items = document.querySelectorAll('.invoice-item');
            let subtotal = 0;
            let totalVat = 0;
            
            items.forEach(item => {
                const quantity = parseFloat(item.querySelector('.quantity-input').value) || 0;
                const price = parseFloat(item.querySelector('.price-input').value) || 0;
                const vatRate = parseFloat(item.querySelector('.vat-input').value) || 0;
                
                const itemTotal = quantity * price;
                const itemVat = itemTotal * (vatRate / 100);
                
                item.querySelector('.item-total').value = formatCurrency(itemTotal + itemVat);
                
                subtotal += itemTotal;
                totalVat += itemVat;
            });
            
            const total = subtotal + totalVat;
            
            const subtotalEl = document.getElementById('invoice-subtotal');
            const vatEl = document.getElementById('invoice-vat');
            const totalEl = document.getElementById('invoice-total');
            
            if (subtotalEl) subtotalEl.textContent = formatCurrency(subtotal);
            if (vatEl) vatEl.textContent = formatCurrency(totalVat);
            if (totalEl) totalEl.textContent = formatCurrency(total);
        }

        async function saveInvoice(e) {
            e.preventDefault();
            
            const saveBtn = e.target.querySelector('button[type="submit"]');
            const saveText = saveBtn.querySelector('#save-text');
            const saveSpinner = saveBtn.querySelector('#save-spinner');
            
            if (saveText) saveText.textContent = 'Se salvează...';
            if (saveSpinner) saveSpinner.classList.remove('hidden');
            
            try {
                const formData = new FormData(e.target);
                const data = {
                    client_id: parseInt(formData.get('client_id')),
                    series_id: parseInt(formData.get('series_id')),
                    issue_date: formData.get('issue_date'),
                    due_date: formData.get('due_date') || null,
                    notes: formData.get('notes') || '',
                    items: []
                };
                
                // Collect items
                const items = document.querySelectorAll('.invoice-item');
                items.forEach((item, index) => {
                    const quantity = parseFloat(item.querySelector('.quantity-input').value) || 0;
                    const price = parseFloat(item.querySelector('.price-input').value) || 0;
                    const vatRate = parseFloat(item.querySelector('.vat-input').value) || 0;
                    const productId = item.querySelector('.product-select').value;
                    
                    if (quantity > 0 && price > 0) {
                        data.items.push({
                            product_id: productId ? parseInt(productId) : null,
                            name: productId ? item.querySelector('.product-select option:checked').text : 'Produs/Serviciu',
                            quantity,
                            unit_price: price,
                            vat_rate: vatRate
                        });
                    }
                });
                
                if (data.items.length === 0) {
                    throw new Error('Adaugă cel puțin un produs/serviciu');
                }
                
                const form = e.target;
                const editId = form.dataset.editId;
                const isEdit = !!editId;
                
                if (isEdit) {
                    await api.put('/invoices/' + editId, data);
                    showToast('Factura a fost actualizată cu succes', 'success');
                } else {
                    await api.post('/invoices', data);
                    showToast('Factura a fost salvată cu succes', 'success');
                }
                const modal = document.getElementById('invoice-modal');
                if (modal) modal.remove();
                await loadInvoices();
                
            } catch (error) {
                showToast('Eroare la salvarea facturii: ' + error.message, 'error');
            } finally {
                if (saveText) saveText.textContent = 'Salvează factura';
                if (saveSpinner) saveSpinner.classList.add('hidden');
            }
        }

        // Quick modal functions for invoice creation
        function showQuickClientModal() {
            // Create modal container
            const modal = document.createElement('div');
            modal.id = 'quick-client-modal';
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';
            
            const modalContent = document.createElement('div');
            modalContent.className = 'flex items-center justify-center min-h-screen p-4';
            
            const modalDialog = document.createElement('div');
            modalDialog.className = 'bg-white rounded-lg shadow-xl max-w-md w-full';
            
            const modalBody = document.createElement('div');
            modalBody.className = 'p-6';
            
            // Header
            const header = document.createElement('div');
            header.className = 'flex justify-between items-center mb-4';
            
            const title = document.createElement('h3');
            title.className = 'text-lg font-medium text-gray-900';
            title.textContent = 'Adaugă client nou';
            
            const closeBtn = document.createElement('button');
            closeBtn.type = 'button';
            closeBtn.className = 'text-gray-400 hover:text-gray-600';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.addEventListener('click', () => modal.remove());
            
            header.appendChild(title);
            header.appendChild(closeBtn);
            
            // Form
            const form = document.createElement('form');
            form.id = 'quick-client-form';
            form.className = 'space-y-4';
            
            // Client type
            const typeDiv = document.createElement('div');
            // @ts-ignore
            typeDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Tip client *</label>
                <select id="quick-client-type" name="type" class="input-field" required>
                    <option value="company">Persoană juridică</option>
                    <option value="individual">Persoană fizică</option>
                </select>
            `;
            
            // Client name
            const nameDiv = document.createElement('div');
            // @ts-ignore
            nameDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Nume client *</label>
                <input type="text" id="quick-client-name" name="name" class="input-field" required placeholder="Nume complet client">
            `;
            
            // CUI field
            const cuiDiv = document.createElement('div');
            cuiDiv.id = 'quick-cui-field';
            // @ts-ignore
            cuiDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">CUI/CIF *</label>
                <input type="text" id="quick-client-cui" name="cui" class="input-field" required placeholder="Ex: RO12345678">
            `;
            
            // CNP field
            const cnpDiv = document.createElement('div');
            cnpDiv.id = 'quick-cnp-field';
            cnpDiv.className = 'hidden';
            // @ts-ignore
            cnpDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">CNP *</label>
                <input type="text" id="quick-client-cnp" name="cnp" class="input-field" placeholder="Ex: 1234567890123">
            `;
            
            // Email
            const emailDiv = document.createElement('div');
            // @ts-ignore
            emailDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input type="email" id="quick-client-email" name="email" class="input-field" placeholder="<EMAIL>">
            `;
            
            // Phone
            const phoneDiv = document.createElement('div');
            // @ts-ignore
            phoneDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Telefon</label>
                <input type="text" id="quick-client-phone" name="phone" class="input-field" placeholder="Ex: 0712345678">
            `;
            
            // Address
            const addressDiv = document.createElement('div');
            // @ts-ignore
            addressDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Adresă</label>
                <input type="text" id="quick-client-address" name="address" class="input-field" placeholder="Adresa completă">
            `;
            
            // City
            const cityDiv = document.createElement('div');
            // @ts-ignore
            cityDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Oraș</label>
                <input type="text" id="quick-client-city" name="city" class="input-field" placeholder="Ex: București">
            `;
            
            // VAT registered
            const vatDiv = document.createElement('div');
            vatDiv.className = 'flex items-center';
            // @ts-ignore
            vatDiv.innerHTML = `
                <input type="checkbox" id="quick-client-vat" name="vat_registered" class="h-4 w-4 text-indigo-600 border-gray-300 rounded">
                <label for="quick-client-vat" class="ml-2 block text-sm text-gray-900">Plătitor de TVA</label>
            `;
            
            // Form actions
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'flex justify-end space-x-3 pt-4 border-t mt-6';
            
            const cancelBtn = document.createElement('button');
            cancelBtn.type = 'button';
            cancelBtn.className = 'btn-secondary';
            cancelBtn.textContent = 'Anulează';
            cancelBtn.addEventListener('click', () => modal.remove());
            
            const saveBtn = document.createElement('button');
            saveBtn.type = 'submit';
            saveBtn.className = 'btn-primary';
            // @ts-ignore
            saveBtn.innerHTML = '<span id="quick-client-save-text">Salvează clientul</span><i id="quick-client-save-spinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>';
            
            actionsDiv.appendChild(cancelBtn);
            actionsDiv.appendChild(saveBtn);
            
            // Assemble form
            form.appendChild(typeDiv);
            form.appendChild(nameDiv);
            form.appendChild(cuiDiv);
            form.appendChild(cnpDiv);
            form.appendChild(emailDiv);
            form.appendChild(phoneDiv);
            form.appendChild(addressDiv);
            form.appendChild(cityDiv);
            form.appendChild(vatDiv);
            form.appendChild(actionsDiv);
            
            // Add event listeners
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                await saveQuickClient(e.target);
            });
            
            // Toggle between company and individual fields
            const clientTypeSelect = form.querySelector('#quick-client-type');
            clientTypeSelect.addEventListener('change', function() {
                const cuiField = document.getElementById('quick-cui-field');
                const cnpField = document.getElementById('quick-cnp-field');
                
                if (this.value === 'individual') {
                    cuiField.classList.add('hidden');
                    cnpField.classList.remove('hidden');
                    document.getElementById('quick-client-cnp').required = true;
                    document.getElementById('quick-client-cui').required = false;
                } else {
                    cuiField.classList.remove('hidden');
                    cnpField.classList.add('hidden');
                    document.getElementById('quick-client-cui').required = true;
                    document.getElementById('quick-client-cnp').required = false;
                }
            });
            
            // Assemble modal
            modalBody.appendChild(header);
            modalBody.appendChild(form);
            modalDialog.appendChild(modalBody);
            modalContent.appendChild(modalDialog);
            modal.appendChild(modalContent);
            
            // Add to DOM
            document.body.appendChild(modal);
        }
        
        // Save quick client and update invoice form
        async function saveQuickClient(form) {
            const saveBtn = document.getElementById('quick-client-save-text');
            const spinner = document.getElementById('quick-client-save-spinner');
            
            saveBtn.textContent = 'Se salvează...';
            spinner.classList.remove('hidden');
            
            try {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                
                // Convert checkbox to boolean
                data.vat_registered = formData.has('vat_registered');
                data.is_individual = data.type === 'individual';
                
                // Save client
                const response = await api.post('/clients', data);
                const newClient = response.data;
                
                // Update client dropdown in invoice form
                const clientSelect = document.getElementById('invoice-client');
                if (clientSelect) {
                    const option = document.createElement('option');
                    option.value = newClient.id;
                    option.textContent = newClient.name;
                    clientSelect.appendChild(option);
                    clientSelect.value = newClient.id;
                }
                
                // Close modal
                document.getElementById('quick-client-modal').remove();
                showToast('Clientul a fost adăugat cu succes', 'success');
                
            } catch (error) {
                showToast(error.message, 'error');
            } finally {
                saveBtn.textContent = 'Salvează clientul';
                spinner.classList.add('hidden');
            }
        }

        function showQuickProductModal(targetSelect) {
            // Create modal container
            const modal = document.createElement('div');
            modal.id = 'quick-product-modal';
            modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 modal z-50';
            
            const modalContent = document.createElement('div');
            modalContent.className = 'flex items-center justify-center min-h-screen p-4';
            
            const modalDialog = document.createElement('div');
            modalDialog.className = 'bg-white rounded-lg shadow-xl max-w-md w-full';
            
            const modalBody = document.createElement('div');
            modalBody.className = 'p-6';
            
            // Header
            const header = document.createElement('div');
            header.className = 'flex justify-between items-center mb-4';
            
            const title = document.createElement('h3');
            title.className = 'text-lg font-medium text-gray-900';
            title.textContent = 'Adaugă produs/serviciu nou';
            
            const closeBtn = document.createElement('button');
            closeBtn.type = 'button';
            closeBtn.className = 'text-gray-400 hover:text-gray-600';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.addEventListener('click', () => modal.remove());
            
            header.appendChild(title);
            header.appendChild(closeBtn);
            
            // Form
            const form = document.createElement('form');
            form.id = 'quick-product-form';
            form.className = 'space-y-4';
            
            // Product type
            const typeDiv = document.createElement('div');
            // @ts-ignore
            typeDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Tip *</label>
                <select id="quick-product-type" name="type" class="input-field" required>
                    <option value="product">Produs</option>
                    <option value="service">Serviciu</option>
                </select>
            `;
            
            // Product name
            const nameDiv = document.createElement('div');
            nameDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Nume *</label>
                <input type="text" id="quick-product-name" name="name" class="input-field" required placeholder="Nume produs/serviciu">
            `;
            
            // Product code
            const codeDiv = document.createElement('div');
            codeDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Cod produs</label>
                <input type="text" id="quick-product-code" name="code" class="input-field" placeholder="Cod intern (opțional)">
            `;
            
            // Description
            const descDiv = document.createElement('div');
            descDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Descriere</label>
                <textarea id="quick-product-description" name="description" class="input-field" rows="2" placeholder="Descriere opțională"></textarea>
            `;
            
            // Price
            const priceDiv = document.createElement('div');
            priceDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Preț unitar (fără TVA) *</label>
                <input type="number" id="quick-product-price" name="unit_price" step="0.01" min="0" class="input-field" required placeholder="0.00">
            `;
            
            // VAT rate
            const vatDiv = document.createElement('div');
            vatDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Cotă TVA *</label>
                <select id="quick-product-vat" name="vat_rate" class="input-field" required>
                    <option value="19">19%</option>
                    <option value="9">9%</option>
                    <option value="5">5%</option>
                    <option value="0">0%</option>
                </select>
            `;
            
            // Unit
            const unitDiv = document.createElement('div');
            unitDiv.innerHTML = `
                <label class="block text-sm font-medium text-gray-700 mb-1">Unitate de măsură *</label>
                <select id="quick-product-unit" name="unit" class="input-field" required>
                    <option value="buc">Bucată</option>
                    <option value="kg">Kilogram</option>
                    <option value="l">Litru</option>
                    <option value="m">Metru</option>
                    <option value="mp">Metru pătrat</option>
                    <option value="mc">Metru cub</option>
                    <option value="ora">Oră</option>
                    <option value="zi">Zi</option>
                    <option value="luna">Lună</option>
                </select>
            `;
            
            // Form actions
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'flex justify-end space-x-3 pt-4 border-t mt-6';
            
            const cancelBtn = document.createElement('button');
            cancelBtn.type = 'button';
            cancelBtn.className = 'btn-secondary';
            cancelBtn.textContent = 'Anulează';
            cancelBtn.addEventListener('click', () => modal.remove());
            
            const saveBtn = document.createElement('button');
            saveBtn.type = 'submit';
            saveBtn.className = 'btn-primary';
            saveBtn.innerHTML = '<span id="quick-product-save-text">Salvează produsul</span><i id="quick-product-save-spinner" class="fas fa-spinner fa-spin ml-2 hidden"></i>';
            
            actionsDiv.appendChild(cancelBtn);
            actionsDiv.appendChild(saveBtn);
            
            // Assemble form
            form.appendChild(typeDiv);
            form.appendChild(nameDiv);
            form.appendChild(codeDiv);
            form.appendChild(descDiv);
            form.appendChild(priceDiv);
            form.appendChild(vatDiv);
            form.appendChild(unitDiv);
            form.appendChild(actionsDiv);
            
            // Add event listeners
            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                await saveQuickProduct(e.target, targetSelect);
            });
            
            // Assemble modal
            modalBody.appendChild(header);
            modalBody.appendChild(form);
            modalDialog.appendChild(modalBody);
            modalContent.appendChild(modalDialog);
            modal.appendChild(modalContent);
            
            // Add to DOM
            document.body.appendChild(modal);
        }
        
        // Save quick product and update invoice form
        async function saveQuickProduct(form, targetSelect) {
            const saveBtn = document.getElementById('quick-product-save-text');
            const spinner = document.getElementById('quick-product-save-spinner');
            
            saveBtn.textContent = 'Se salvează...';
            spinner.classList.remove('hidden');
            
            try {
                const formData = new FormData(form);
                const data = Object.fromEntries(formData);
                
                // Convert numeric fields
                data.unit_price = parseFloat(data.unit_price);
                data.vat_rate = parseInt(data.vat_rate);
                
                // Save product
                const response = await api.post('/products', data);
                const newProduct = response.data;
                
                // Update product dropdown in invoice form
                if (targetSelect) {
                    // Find the row containing the target select
                    const row = targetSelect.closest('tr');
                    if (row) {
                        // Set the product in the dropdown
                        targetSelect.innerHTML += `<option value="${newProduct.id}" data-price="${newProduct.unit_price}" data-vat="${newProduct.vat_rate}">${newProduct.name}</option>`;
                        targetSelect.value = newProduct.id;
                        
                        // Trigger change event to update price and VAT
                        const changeEvent = new Event('change');
                        targetSelect.dispatchEvent(changeEvent);
                    }
                }
                
                // Close modal
                document.getElementById('quick-product-modal').remove();
                showToast('Produsul a fost adăugat cu succes', 'success');
                
            } catch (error) {
                showToast(error.message, 'error');
            } finally {
                saveBtn.textContent = 'Salvează produsul';
                spinner.classList.add('hidden');
            }
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Check for existing session
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');

            if (token && user) {
                app.token = token;
                app.user = JSON.parse(user);
                showApp();
            } else {
                showLogin();
            }

            // Login form
            document.getElementById('login-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                const email = formData.get('email');
                const password = formData.get('password');

                const loginBtn = document.getElementById('login-text');
                const spinner = document.getElementById('login-spinner');

                loginBtn.textContent = 'Se autentifică...';
                spinner.classList.remove('hidden');

                const success = await login(email, password);

                loginBtn.textContent = 'Autentificare';
                spinner.classList.add('hidden');

                if (success) {
                    e.target.reset();
                }
            });

            // Register form
            document.getElementById('register-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);

                const registerBtn = document.getElementById('register-text');
                const spinner = document.getElementById('register-spinner');

                registerBtn.textContent = 'Se înregistrează...';
                spinner.classList.remove('hidden');

                const success = await register(data);

                registerBtn.textContent = 'Înregistrare';
                spinner.classList.add('hidden');

                if (success) {
                    e.target.reset();
                }
            });

            // Navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = this.dataset.page;
                    if (page) {
                        loadPage(page);
                    }
                });
            });

            // Show register/login links
            document.getElementById('show-register').addEventListener('click', function(e) {
                e.preventDefault();
                showRegister();
            });

            document.getElementById('show-login').addEventListener('click', function(e) {
                e.preventDefault();
                showLogin();
            });

            // Logout
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });

            // Mobile menu
            document.getElementById('mobile-menu-btn').addEventListener('click', function() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('-translate-x-full');
            });

            // Toast close
            document.getElementById('toast-close').addEventListener('click', function() {
                document.getElementById('toast').classList.add('hidden');
            });
        });
    </script>
</body>
</html>`);
});

// API Routes
app.route('/api/auth', authRoutes);

// Protected routes (require authentication)
app.use('/api/*', authMiddleware);
app.route('/api/invoices', invoices);
app.route('/api/clients', clientRoutes);
app.route('/api/anaf', anaf);
app.route('/api/products', productRoutes);
app.route('/api/companies', companyRoutes);
app.route('/api/dashboard', dashboardRoutes);
app.route('/api/reports', reportRoutes);
app.route('/api/settings', settingsRoutes);
app.route('/api/payments', paymentRoutes);
app.route('/api/credit-notes', creditNoteRoutes);
app.route('/api/receipts', receiptRoutes);

// Migration route for adding new features
app.post('/api/migrate', async (c) => {
  try {
    // Migration SQL statements
    const statements = [
      `CREATE TABLE IF NOT EXISTS recurring_invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL,
        client_id INTEGER NOT NULL,
        series_id INTEGER NOT NULL,
        template_name TEXT NOT NULL,
        frequency TEXT NOT NULL CHECK (frequency IN ('weekly', 'monthly', 'quarterly', 'yearly')),
        interval_count INTEGER NOT NULL DEFAULT 1,
        start_date DATE NOT NULL,
        end_date DATE,
        next_generation_date DATE NOT NULL,
        last_generated_date DATE,
        is_active BOOLEAN DEFAULT TRUE,
        auto_send_email BOOLEAN DEFAULT FALSE,
        notes TEXT,
        payment_terms TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE IF NOT EXISTS recurring_invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recurring_id INTEGER NOT NULL,
        product_id INTEGER,
        name TEXT NOT NULL,
        description TEXT,
        quantity DECIMAL(10,4) NOT NULL,
        unit TEXT NOT NULL DEFAULT 'buc',
        unit_price DECIMAL(10,4) NOT NULL,
        vat_rate DECIMAL(5,2) NOT NULL,
        sort_order INTEGER DEFAULT 0
      )`,
      `CREATE TABLE IF NOT EXISTS payment_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        payment_provider TEXT NOT NULL,
        payment_id TEXT NOT NULL,
        amount DECIMAL(12,4) NOT NULL,
        currency TEXT NOT NULL DEFAULT 'RON',
        status TEXT NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
        transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        webhook_data TEXT,
        notes TEXT
      )`,
      `CREATE INDEX IF NOT EXISTS idx_recurring_invoices_company ON recurring_invoices(company_id)`,
      `CREATE INDEX IF NOT EXISTS idx_recurring_invoices_next_date ON recurring_invoices(next_generation_date)`,
      `CREATE INDEX IF NOT EXISTS idx_recurring_items_recurring ON recurring_invoice_items(recurring_id)`,
      `CREATE INDEX IF NOT EXISTS idx_payment_transactions_invoice ON payment_transactions(invoice_id)`
    ];

    // Execute migration
    for (const statement of statements) {
      await c.env.DB.prepare(statement).run();
    }

    return c.json({
      success: true,
      message: 'Migration completed successfully'
    });
  } catch (error) {
    console.error('Migration error:', error);
    return c.json({
      success: false,
      message: 'Migration failed: ' + (error instanceof Error ? error.message : String(error))
    }, 500);
  }
});

// Error handler
app.onError(errorHandler);

export default app;